/**
 * 提示词组装器
 */
import { PromptModule, PromptAssemblyOptions } from './types';
import { promptModuleRegistry } from './registry';

/**
 * 提示词组装器类
 */
export class PromptAssembler {
  /**
   * 组装提示词
   * @param options 组装选项
   * @returns 组装后的提示词字符串
   */
  static assemble(options: PromptAssemblyOptions = {}): string {
    // 获取要包含的模块
    const modules = PromptAssembler.resolveModules(options);
    
    // 按顺序排序模块
    const sortedModules = PromptAssembler.sortModules(modules);
    
    // 组装提示词
    let promptText = `<system>你是一个通用的超级AI智能体，能够深度思考，精准理解用户的意图，扮演不同的角色，制定计划，实用工具，编写文档，完成任务，反思纠错，高质量完成用户提出的各种需求。</system> 以下是你运作的详细说明：\n`;
    
    // 添加模块内容
    sortedModules.forEach(module => {
      promptText += PromptAssembler.processModuleContent(module, options.variables || {});
    });
    
    // 添加扩展点
    promptText += `\n<extension-point>\n  <!-- 此区域预留用于后续扩展其他任务类型的指南 -->\n</extension-point>`;
    
    return promptText;
  }
  
  /**
   * 解析要包含的模块
   * @param options 组装选项
   * @returns 要包含的模块数组
   */
  private static resolveModules(options: PromptAssemblyOptions): PromptModule[] {
    // 默认包含所有核心模块
    let modules = promptModuleRegistry.getCoreModules();
    
    // 如果指定了要包含的模块，则添加这些模块及其依赖
    if (options.includeModules && options.includeModules.length > 0) {
      const additionalModules = promptModuleRegistry.getAllDependencies(options.includeModules);
      
      // 合并模块，避免重复
      const moduleMap = new Map<string, PromptModule>();
      [...modules, ...additionalModules].forEach(module => {
        moduleMap.set(module.id, module);
      });
      
      modules = Array.from(moduleMap.values());
    }
    
    // 如果指定了要排除的模块，则移除这些模块
    if (options.excludeModules && options.excludeModules.length > 0) {
      modules = modules.filter(module => !options.excludeModules?.includes(module.id));
    }
    
    // 添加自定义模块
    if (options.customModules && options.customModules.length > 0) {
      modules = [...modules, ...options.customModules];
    }
    
    return modules;
  }
  
  /**
   * 按顺序排序模块
   * @param modules 模块数组
   * @returns 排序后的模块数组
   */
  private static sortModules(modules: PromptModule[]): PromptModule[] {
    return [...modules].sort((a, b) => {
      const orderA = a.order || 999;
      const orderB = b.order || 999;
      return orderA - orderB;
    });
  }
  
  /**
   * 处理模块内容，替换变量
   * @param module 模块
   * @param variables 变量映射
   * @returns 处理后的模块内容
   */
  private static processModuleContent(module: PromptModule, variables: Record<string, string>): string {
    let content = module.content;
    
    // 替换变量
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      content = content.replace(regex, value);
    });
    
    return content;
  }
}

/**
 * 创建默认提示词
 * @returns 默认提示词字符串
 */
export function createDefaultPrompt(): string {
  return PromptAssembler.assemble();
}

/**
 * 创建演示文稿提示词
 * @returns 演示文稿提示词字符串
 */
export function createPresentationPrompt(): string {
  return PromptAssembler.assemble({
    includeModules: ['presentation-guidelines']
  });
}

/**
 * 创建数据可视化提示词
 * @returns 数据可视化提示词字符串
 */
export function createDataVisualizationPrompt(): string {
  return PromptAssembler.assemble({
    includeModules: ['data-visualization']
  });
}

/**
 * 创建自定义提示词
 * @param includeModules 要包含的模块ID数组
 * @param excludeModules 要排除的模块ID数组
 * @param variables 变量映射
 * @returns 自定义提示词字符串
 */
export function createCustomPrompt(
  includeModules?: string[],
  excludeModules?: string[],
  variables?: Record<string, string>
): string {
  return PromptAssembler.assemble({
    includeModules,
    excludeModules,
    variables
  });
}
