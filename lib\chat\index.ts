/**
 * 聊天服务
 * 提供统一的聊天接口，封装不同的Provider实现
 */

import { ProviderFactory, ChatCompletionOptions, ChatCompletionResponse, Message, ProviderConfig } from '../providers';
import { getModelById, ModelProvider } from '../models';
import { CustomModel } from '../ai-store';

export interface ChatServiceConfig {
  providers: Record<ModelProvider, ProviderConfig>;
  customModels?: CustomModel[];
}

/**
 * 聊天服务类
 * 根据模型自动选择对应的Provider
 */
export class ChatService {
  private config: ChatServiceConfig;
  private providerInstances: Map<ModelProvider, any> = new Map();

  constructor(config: ChatServiceConfig) {
    this.config = config;
  }

  /**
   * 获取Provider实例
   */
  private getProviderForModel(modelId: string): any {
    // 首先查找内置模型
    let modelConfig = getModelById(modelId);
    let providerType: ModelProvider;

    if (modelConfig) {
      providerType = modelConfig.provider;
    } else {
      // 查找自定义模型
      const customModel = this.config.customModels?.find(m => m.id === modelId);
      if (customModel) {
        providerType = customModel.provider;
      } else {
        throw new Error(`未找到模型配置: ${modelId}`);
      }
    }

    // 检查是否已经创建了Provider实例
    if (!this.providerInstances.has(providerType)) {
      const providerConfig = this.config.providers[providerType];
      if (!providerConfig) {
        throw new Error(`未找到提供商配置: ${providerType}`);
      }

      // 创建Provider实例，传递自定义模型
      const configWithCustomModels = {
        ...providerConfig,
        customModels: this.config.customModels
      };
      const provider = ProviderFactory.createProvider(providerType, configWithCustomModels);
      this.providerInstances.set(providerType, provider);
    }

    return this.providerInstances.get(providerType);
  }

  /**
   * 聊天完成方法
   */
  async chatCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    const provider = this.getProviderForModel(options.model);
    return await provider.chatCompletion(options);
  }

  /**
   * 流式聊天完成方法
   */
  async streamingChatCompletion(
    options: ChatCompletionOptions,
    onContent: (content: string) => void,
    onError: (error: Error) => void,
    onFinish: (response: ChatCompletionResponse) => void,
    onReasoningContent?: (reasoningContent: string) => void
  ): Promise<void> {
    const provider = this.getProviderForModel(options.model);
    await provider.streamingChatCompletion(options, onContent, onError, onFinish, onReasoningContent);
  }
}

/**
 * 创建聊天服务实例
 */
export function createChatService(config: ChatServiceConfig): ChatService {
  return new ChatService(config);
}

/**
 * 从环境变量和配置创建默认聊天服务
 */
export function createDefaultChatService(customConfig?: Partial<ChatServiceConfig>): ChatService {
  const defaultConfig: ChatServiceConfig = {
    providers: {
      openai: {
        apiKey: process.env.OPENAI_API_KEY || '',
        baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1'
      },
      xai: {
        apiKey: process.env.XAI_API_KEY || '',
        baseUrl: process.env.XAI_BASE_URL || 'https://api.groq.com/openai/v1'
      },
      deepseek: {
        apiKey: process.env.DEEPSEEK_API_KEY || '',
        baseUrl: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1'
      },
      anthropic: {
        apiKey: process.env.ANTHROPIC_API_KEY || '',
        baseUrl: process.env.ANTHROPIC_BASE_URL
      }
    }
  };

  // 合并自定义配置
  const config: ChatServiceConfig = {
    providers: {
      ...defaultConfig.providers,
      ...(customConfig?.providers || {})
    },
    customModels: customConfig?.customModels || []
  };

  return createChatService(config);
}
