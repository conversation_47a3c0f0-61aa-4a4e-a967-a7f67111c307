/**
 * AI客户端
 * 提供统一的AI服务接口，集成模型管理和聊天功能
 */

import { createDefaultChatService, ChatService } from './chat/index';
import { getAllModels, getModelsByProvider, getModelById, ModelProvider, ModelConfig } from './models';
import { ChatCompletionOptions, ChatCompletionResponse, Message } from './providers/base';

/**
 * AI客户端配置
 */
export interface AIClientConfig {
  apiKeys?: Record<ModelProvider, string>;
  baseUrls?: Record<ModelProvider, string>;
  customModels?: Array<{
    id: string;
    name: string;
    provider: ModelProvider;
    maxTokens?: number;
    temperature?: number;
  }>;
}

/**
 * AI客户端类
 * 提供统一的AI服务接口
 */
export class AIClient {
  private chatService: ChatService;
  private customModels: AIClientConfig['customModels'];

  constructor(config?: AIClientConfig) {
    this.customModels = config?.customModels || [];

    // 创建聊天服务
    this.chatService = createDefaultChatService({
      providers: {
        openai: {
          apiKey: config?.apiKeys?.openai || '',
          baseUrl: config?.baseUrls?.openai || 'https://api.openai.com/v1'
        },
        xai: {
          apiKey: config?.apiKeys?.xai || '',
          baseUrl: config?.baseUrls?.xai || 'https://api.x.ai/v1'
        },
        deepseek: {
          apiKey: config?.apiKeys?.deepseek || '',
          baseUrl: config?.baseUrls?.deepseek || 'https://api.deepseek.com/v1'
        },
        anthropic: {
          apiKey: config?.apiKeys?.anthropic || '',
          baseUrl: config?.baseUrls?.anthropic
        }
      },
      customModels: this.customModels
    });
  }

  /**
   * 获取所有支持的模型
   */
  getAllModels(): ModelConfig[] {
    return getAllModels();
  }

  /**
   * 根据提供商获取模型列表
   */
  getModelsByProvider(provider: ModelProvider): ModelConfig[] {
    return getModelsByProvider(provider);
  }

  /**
   * 根据ID获取模型配置
   */
  getModelById(id: string): ModelConfig | undefined {
    return getModelById(id);
  }

  /**
   * 获取模型配置（包括自定义模型）
   */
  private getModelConfig(modelId: string) {
    // 首先查找自定义模型
    const customModel = this.customModels?.find(m => m.id === modelId);
    if (customModel) {
      return {
        maxTokens: customModel.maxTokens,
        temperature: customModel.temperature
      };
    }

    // 然后查找内置模型
    const builtInModel = getModelById(modelId);
    if (builtInModel) {
      return {
        maxTokens: builtInModel.maxTokens,
        temperature: builtInModel.temperature
      };
    }

    return {};
  }

  /**
   * 聊天完成方法
   */
  async chatCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    // 合并模型配置
    const modelConfig = this.getModelConfig(options.model);
    const mergedOptions = {
      ...options,
      maxTokens: options.maxTokens ?? modelConfig.maxTokens,
      temperature: options.temperature ?? modelConfig.temperature
    };

    return await this.chatService.chatCompletion(mergedOptions);
  }

  /**
   * 流式聊天完成方法
   */
  async streamingChatCompletion(
    options: ChatCompletionOptions,
    onContent: (content: string) => void,
    onError: (error: Error) => void,
    onFinish: (response: ChatCompletionResponse) => void,
    onReasoningContent?: (reasoningContent: string) => void
  ): Promise<void> {
    // 合并模型配置
    const modelConfig = this.getModelConfig(options.model);
    const mergedOptions = {
      ...options,
      maxTokens: options.maxTokens ?? modelConfig.maxTokens,
      temperature: options.temperature ?? modelConfig.temperature
    };

    await this.chatService.streamingChatCompletion(mergedOptions, onContent, onError, onFinish, onReasoningContent);
  }
}

/**
 * 创建AI客户端实例
 */
export function createAIClient(config?: AIClientConfig): AIClient {
  return new AIClient(config);
}

// 导出类型定义
export type { ModelProvider, ModelConfig, Message, ChatCompletionOptions, ChatCompletionResponse };

// 创建默认客户端实例
export const defaultAIClient = createAIClient();
