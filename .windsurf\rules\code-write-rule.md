---
trigger: always_on
---

# 前端开发AI代码生成踩坑指南

## React/Vue 状态管理

1. **React状态更新的异步性**：
   - 永远不要假设 `setState` 是同步的
   - 错误示例：`setCount(count + 1); console.log(count)` // count 仍是旧值
   - 正确做法：使用 `useEffect` 监听状态变化或函数式更新 `setCount(prev => prev + 1)`

2. **闭包陷阱**：
   - 事件处理函数和定时器中容易捕获旧的状态值
   - 错误示例：`setTimeout(() => console.log(count), 1000)` // 可能打印旧值
   - 正确做法：使用 `useRef` 或 `useCallback` 确保访问最新状态

3. **状态依赖链**：
   - 避免在一个 useEffect 中更新多个相互依赖的状态
   - 错误示例：连续调用多个 setState 可能导致状态不一致
   - 正确做法：使用 `useReducer` 或合并状态更新

4. **初始化状态的计算**：
   - 避免在每次渲染时重复计算初始状态
   - 错误示例：`useState(expensiveCalculation())`
   - 正确做法：`useState(() => expensiveCalculation())`

## 组件设计与Props

5. **Props 类型定义**：
   - 必须为所有 Props 定义 TypeScript 接口
   - 避免使用 `any` 类型，使用具体的联合类型
   - 为可选属性提供默认值：`const { size = 'medium' } = props`

6. **Props 验证与默认值**：
   - 对于复杂对象 Props，使用 `React.memo` 配合自定义比较函数
   - 避免将函数作为内联 Props 传递：`onClick={() => handleClick()}`
   - 正确做法：使用 `useCallback` 包装事件处理函数

7. **组件职责分离**：
   - 单个组件不应超过 200 行代码
   - 业务逻辑和 UI 逻辑分离
   - 抽取自定义 Hook 处理复杂逻辑

## 性能优化

8. **不必要的重渲染**：
   - 避免在 JSX 中创建内联对象：`style={{color: 'red'}}`
   - 正确做法：将样式对象提取到组件外部或使用 CSS 类
   - 使用 `React.memo` 包装纯展示组件

9. **列表渲染优化**：
   - 必须为列表项提供稳定的 `key` 属性
   - 避免使用数组索引作为 key：`key={index}`
   - 大列表（>100 项）使用虚拟滚动库如 `react-window`

10. **内存泄漏防护**：
    - 在 `useEffect` 清理函数中取消订阅和清理定时器
    - 组件卸载时取消进行中的 API 请求
    - 正确示例：`useEffect(() => { const timer = setInterval(...); return () => clearInterval(timer); }, [])`

## 异步处理与API调用

11. **Promise 错误处理**：
    - 所有 async/await 必须包含 try-catch
    - 避免未处理的 Promise rejection
    - 使用 AbortController 取消请求：`fetch(url, { signal: controller.signal })`

12. **Loading 状态管理**：
    - 为每个异步操作维护独立的 loading 状态
    - 避免全局 loading 影响用户体验
    - 实现骨架屏而不是简单的 "加载中" 文字

13. **数据缓存策略**：
    - 避免重复请求相同数据
    - 使用 SWR 或 React Query 管理服务端状态
    - 实现适当的缓存失效机制

## 表单处理

14. **受控组件规范**：
    - 所有表单元素必须是受控组件
    - 避免 `defaultValue` 和 `value` 混用
    - 使用 `react-hook-form` 处理复杂表单

15. **表单验证**：
    - 实时验证和提交验证相结合
    - 错误信息必须对用户友好
    - 使用 schema 验证库如 `yup` 或 `zod`

## 样式管理

16. **CSS-in-JS 最佳实践**：
    - 避免在渲染函数中定义样式对象
    - 使用主题系统管理设计令牌
    - 样式组件命名要语义化：`const Button = styled.button` 而不是 `const StyledDiv`

17. **响应式设计**：
    - 优先使用 Flexbox 和 CSS Grid
    - 断点必须基于内容而不是设备
    - 使用相对单位（rem, em, %）而不是固定像素

## 错误处理

18. **错误边界实现**：
    - 根组件必须包含错误边界
    - 为不同模块实现细粒度错误边界
    - 错误信息要包含足够的上下文用于调试

19. **用户友好的错误提示**：
    - 避免直接显示技术错误信息
    - 提供用户可操作的错误恢复选项
    - 重要操作失败后提供重试机制

## 代码质量

20. **函数复杂度控制**：
    - 单个函数不应超过 20 行
    - 嵌套层级不超过 3 层
    - 使用早期返回减少嵌套：`if (error) return <ErrorComponent />`

21. **命名规范**：
    - 组件名使用 PascalCase：`UserProfile`
    - Hook 名必须以 use 开头：`useUserData`
    - 事件处理函数使用 handle 前缀：`handleSubmit`

22. **注释和文档**：
    - 复杂逻辑必须添加注释说明
    - 公共组件必须提供 JSDoc 文档
    - 使用 Storybook 记录组件使用示例

## 安全性

23. **XSS 防护**：
    - 避免使用 `dangerouslySetInnerHTML`
    - 对用户输入进行适当的转义
    - 使用内容安全策略（CSP）

24. **敏感信息处理**：
    - API 密钥不能出现在前端代码中
    - 使用环境变量管理配置
    - 敏感操作需要二次确认

## 测试要求

25. **测试覆盖率**：
    - 关键组件测试覆盖率不低于 80%
    - 使用 React Testing Library 进行用户行为测试
    - Mock 外部依赖和 API 调用

26. **端到端测试**：
    - 关键用户流程必须有 E2E 测试覆盖
    - 使用 Cypress 或 Playwright
    - 测试不同浏览器的兼容性

## 构建和部署

27. **代码分割**：
    - 路由级别的代码分割
    - 大型第三方库异步加载
    - 使用 `React.lazy` 和 `Suspense`

28. **资源优化**：
    - 图片必须经过压缩和格式优化
    - 使用 CDN 加速静态资源
    - 实现适当的缓存策略

## AI 代码生成特定规则

29. **提示词质量**：
    - 明确指定技术栈和版本
    - 提供具体的需求和约束条件
    - 包含错误处理和边界情况要求

30. **代码审查检查点**：
    - 验证生成代码的类型安全性
    - 检查是否遵循项目编码规范
    - 确认性能优化措施是否到位
    - 验证错误处理是否完整
