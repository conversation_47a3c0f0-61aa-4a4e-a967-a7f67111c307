/**
 * promptTemplates.ts
 * 文件生成格式相关的提示词模板配置，可扩展多格式/多语言。
 */

export type FileFormat = 'html' | 'markdown';

export interface FilePromptTemplate {
  format: FileFormat;
  template: string;
}

export interface PromptTemplatesConfig {
  fileInstructions: Record<FileFormat, string>;
  multiFileNote: string;
}

export const promptTemplates: PromptTemplatesConfig = {
  fileInstructions: {
    html: `如果需要生成HTML代码，严格使用以下格式包裹代码，以便我可以正确提取：
    
    \`\`\`html{filename=文件名.html}
    你的HTML代码
    \`\`\`
    
    **资源链接：**
    <!-- 必要的CSS库 -->
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    ...
    <!-- 数据可视化相关CSS -->
    <!-- ECharts主题CSS -->
    <link href="https://cdn.jsdelivr.net/npm/echarts@5/theme/dark.css" rel="stylesheet">
    ...
    <!-- 必要的JavaScript库 -->
    <!-- 图片库 -->
    <!-- Unsplash API -->
    <script src="https://cdn.jsdelivr.net/npm/unsplash-js@7.0.15/dist/unsplash.min.js"></script>
    <!-- Pexels API -->
    <script src="https://cdn.jsdelivr.net/npm/pexels@1.3.0/dist/pexels.min.js"></script>
    <!-- 图片占位生成器 -->
    <!-- 头像占位 -->
    <img src="https://ui-avatars.com/api/?name=John+Doe" alt="Avatar">
    <img src="https://avatars.dicebear.com/api/avataaars/john.svg" alt="Avatar">
    <!-- 随机图片 -->
    <img src="https://source.unsplash.com/random/800x600" alt="Random">
    <img src="https://picsum.photos/800/600" alt="Random">
    <!-- 自定义占位图 -->
    <img src="https://via.placeholder.com/150/0000FF/FFFFFF?text=Hello" alt="Custom">
    <!-- SVG图形库 -->
    <!-- SVG.js -->
    <script src="https://cdn.jsdelivr.net/npm/@svgdotjs/svg.js"></script>
    <!-- Snap.svg -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/snap.svg/0.5.1/snap.svg-min.js"></script>
    <!-- Rough.js -->
    <script src="https://cdn.jsdelivr.net/npm/roughjs@4.5.2/bundled/rough.js"></script>
    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- ECharts完整版 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
    <!-- 数据处理库 -->
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>
    <!-- 时间处理库 -->
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.1/moment.min.js"></script>
    <!-- D3.js -->
    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <!-- mermaid -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    ...
    使用建议：
    1. 根据实际需要选择性包含所需的库,包含但不限于上面的库
    2. 使用CDN加速资源加载
    3. 添加适当的错误处理
    4. 根据需要添加加载动画
    5. 考虑浏览器兼容性
    6. 添加适当的注释说明
     
    **重要提示：**
    * 请提供一个完整、可运行的HTML文件，包含所有必要的CSS和JavaScript
    * 确保代码符合W3C标准
    * 系统当前不支持同名文件增量更新，只能全量输出
    * 不要在HTML代码块内嵌套其他代码块标记
    * 不要在HTML代码外使用任务标记或其他非相关内容
    * 代码块前后不要添加任何无关内容或解释`,
    
      markdown: `如果需要生成Markdown代码，严格使用以下格式包裹代码，以便我可以正确提取：
    
    \`\`\`markdown{filename=文件名.md}
    你的Markdown代码
    \`\`\`
    
    **重要提示：**
    * 确保Markdown代码完整、格式正确
    * 不要在Markdown代码块内嵌套其他代码块标记
    * 不要在Markdown代码外使用任务标记或其他非相关内容
    * 代码块前后不要添加任何无关内容或解释`
    },
  multiFileNote: `如果需要生成多个文件，请为每个文件使用上述格式，并确保每个文件都有唯一的文件名。`,
};
