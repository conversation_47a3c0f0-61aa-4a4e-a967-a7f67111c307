import { NextRequest, NextResponse } from 'next/server';
import { createAIClient, Message } from '@/lib/ai-client';
import { ModelProvider, getModelById } from '@/lib/models';
import { createDefaultPrompt, createPresentationPrompt, createDataVisualizationPrompt } from '@/lib/prompt-modules';
import { AIConfigManager } from '@/lib/ai-store';
import { cookies } from 'next/headers';

/**
 * 检测内容是否不完整
 * 使用多种启发式方法检测内容是否被截断
 */
function checkIncompleteContent(content: string): boolean {
  if (!content || content.trim().length === 0) {
    return false;
  }

  // 1. 检查未闭合的代码块
  const codeBlockMatches = content.match(/```/g);
  if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {
    console.log('检测到未闭合的代码块');
    return true;
  }

  // 2. 检查未闭合的HTML标签
  const htmlTagPattern = /<(\w+)(?:\s[^>]*)?>/g;
  const closingTagPattern = /<\/(\w+)>/g;

  const openTags = [];
  let match;

  // 收集所有开放标签
  while ((match = htmlTagPattern.exec(content)) !== null) {
    const tagName = match[1].toLowerCase();
    // 跳过自闭合标签
    if (!['img', 'br', 'hr', 'input', 'meta', 'link'].includes(tagName)) {
      openTags.push(tagName);
    }
  }

  // 移除已闭合的标签
  while ((match = closingTagPattern.exec(content)) !== null) {
    const tagName = match[1].toLowerCase();
    const index = openTags.lastIndexOf(tagName);
    if (index !== -1) {
      openTags.splice(index, 1);
    }
  }

  if (openTags.length > 0) {
    console.log('检测到未闭合的HTML标签:', openTags);
    return true;
  }

  // 3. 检查内容是否以不完整的句子结尾
  const trimmedContent = content.trim();
  const lastChar = trimmedContent[trimmedContent.length - 1];

  // 如果内容以这些字符结尾，可能是不完整的
  const incompleteEndings = [',', '，', '、', '：', ':', ';', '；', '(', '（', '[', '{'];
  if (incompleteEndings.includes(lastChar)) {
    console.log('检测到不完整的句子结尾:', lastChar);
    return true;
  }

  // 4. 检查是否以不完整的单词结尾（英文）
  const words = trimmedContent.split(/\s+/);
  const lastWord = words[words.length - 1];
  if (lastWord && /^[a-zA-Z]+$/.test(lastWord) && lastWord.length > 1 && !lastWord.match(/[.!?]$/)) {
    // 检查是否是常见的完整单词
    const commonCompleteWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    if (!commonCompleteWords.includes(lastWord.toLowerCase())) {
      console.log('检测到可能不完整的英文单词:', lastWord);
      return true;
    }
  }

  return false;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { messages, model, debug } = body;
    // 原始消息，可能包含 'task' 角色
    const rawMessages = messages as Array<{ role: string; content: string }>;

    // 输出详细的消息统计信息
    console.log('原始消息个数:', rawMessages.length);

    // 如果提供了调试信息，输出更多详细信息
    if (debug) {
      console.log('调试信息:', debug);
      console.log(`任务${debug.taskNumber} - 消息统计: API收到消息数=${rawMessages.length}, 原始消息数=${debug.originalMessageCount}`);

      // 输出消息类型分布
      const messageRoles = rawMessages.reduce((acc, msg) => {
        acc[msg.role] = (acc[msg.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      console.log(`任务${debug.taskNumber} - 消息角色分布:`, messageRoles);
    }

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: 'Invalid messages format' },
        { status: 400 }
      );
    }

    // 在没有 system 消息时，前置系统提示
    if (!rawMessages.some(msg => msg.role === 'system')) {
      let systemPrompt = createPresentationPrompt();
      // console.log('使用系统提示:', systemPrompt);

      rawMessages.unshift({
        role: 'system',
        content: systemPrompt
      });
    }

    // 为API准备消息：将自定义 'task' 角色映射为 'user'
    const apiMessages: Message[] = rawMessages.map(msg => ({
      role: msg.role === 'task' ? 'user' : (msg.role as 'user' | 'assistant' | 'system'),
      content: msg.content,
    }));

    // 过滤掉空内容消息，避免 API 报错
    const filteredMessages = apiMessages.filter(m => m.content?.trim());
    console.log('过滤后的消息个数:', filteredMessages.length);
    // 获取API密钥和基础URL
    const providerCookie = request.cookies.get('ai_provider');
    const modelCookie = request.cookies.get('ai_model');

    // 获取模型配置
    const cookieStore = await cookies();
    const configManager = new AIConfigManager(cookieStore);
    const { customModels } = await configManager.getConfig();
    const modelConfig = getModelById(model, customModels);

    // 根据模型配置或模型名称确定提供商
    let provider: ModelProvider;

    console.log('模型名称:', model);
    console.log('找到的模型配置:', modelConfig);

    // 优先使用请求中指定的提供商，如果有的话
    if (providerCookie?.value) {
      provider = providerCookie.value as ModelProvider;
      console.log('使用请求中指定的提供商:', provider);
    }
    // 其次使用模型配置中的提供商
    else if (modelConfig && 'provider' in modelConfig) {
      provider = modelConfig.provider as ModelProvider;
      console.log('使用模型配置中的提供商:', provider);
    }
    // 最后根据模型名称推断提供商
    else if (model.startsWith('gpt-') || model === 'gpt-4o') {
      provider = 'openai';
    } else if (model.startsWith('grok-')) {
      provider = 'xai';
    } else if (model === 'deepseek-coder' || model.startsWith('deepseek-')) {
      provider = 'deepseek';
    } else {
      // 对于自定义模型，如果没有明确指定提供商，默认使用 OpenAI 兼容接口
      provider = 'openai';
      console.log('使用默认提供商 OpenAI 兼容接口');
    }

    console.log('识别的提供商:', provider);

    // 获取特定提供商的API密钥和基础URL
    const apiKeyCookie = request.cookies.get(`ai_api_key_${provider}`);
    const baseUrlCookie = request.cookies.get(`ai_base_url_${provider}`);
    const apiKey = apiKeyCookie?.value || '';
    const baseUrl = baseUrlCookie?.value || '';

    console.log('API调用配置:', {
      model,
      provider,
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey?.length || 0,
      hasBaseUrl: !!baseUrl,
      baseUrlLength: baseUrl?.length || 0,
      cookieName: `ai_api_key_${provider}`
    });

    // 创建API密钥和基础URL对象
    const apiKeys: Partial<Record<ModelProvider, string>> = {};
    apiKeys[provider as ModelProvider] = apiKey;

    const baseUrls: Partial<Record<ModelProvider, string>> = {};
    if (baseUrl) {
      baseUrls[provider as ModelProvider] = baseUrl;
    }

    // 创建AI客户端
    const aiClient = createAIClient({
      apiKeys: apiKeys as Record<ModelProvider, string>,
      baseUrls: baseUrls as Record<ModelProvider, string>,
      customModels: customModels
    });

    // 调用流式聊天完成
    const encoder = new TextEncoder();
    let fullContent = '';
    let isIncomplete = false;

    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          let reasoningContent = ''; // 用于收集推理内容

          await aiClient.streamingChatCompletion(
            {
              model,
              messages: filteredMessages,
            },
            // onContent callback
            (content: string) => {
              fullContent += content;
              controller.enqueue(encoder.encode(content));
            },
            // onError callback
            (error: Error) => {
              console.error('流式聊天错误:', error);
              controller.error(error);
            },
            // onFinish callback
            async (response) => {
              // 如果有推理内容，记录到日志并发送给前端
              if (response.reasoningContent) {
                console.log('DeepSeek推理内容:', response.reasoningContent.substring(0, 100) + '...');
                // 发送推理内容给前端，使用特殊标记
                controller.enqueue(encoder.encode(`[REASONING_CONTENT]${response.reasoningContent}[/REASONING_CONTENT]`));
              }

              console.log('流式聊天完成:', response);

              // 检测是否因为token限制而截断 - 使用更准确的方法
              let isTokenLimitReached = false;
              let hasStopReasonLength = false;

              // 1. 检查 stop_reason 是否为 'length' (最可靠的方法)，max_tokens，length
              // 使用类型断言来访问可能存在的属性
              const responseAny = response as any;
              const stopReason = responseAny.stop_reason;
              const finishReason = responseAny.finish_reason;

              if (stopReason === 'length' || stopReason === 'max_tokens' || finishReason === 'length' || finishReason === 'max_tokens') {
                hasStopReasonLength = true;
                console.log('检测到 stop_reason/finish_reason 为 length，内容被截断');
              }

              // 2. 检查 usage 信息中的 token 使用情况
              if (response.usage) {
                const maxTokens = modelConfig?.maxTokens ?? 8192;
                const completionTokens = response.usage.completionTokens;

                // 如果使用的 token 接近或达到最大限制，认为可能被截断
                if (completionTokens && completionTokens >= maxTokens * 0.98) {
                  isTokenLimitReached = true;
                  console.log(`检测到 token 使用量接近限制: ${completionTokens}/${maxTokens}`);
                }
              }

              // 3. 检测内容是否不完整（更严格的启发式检测）
              const hasIncompleteContent = checkIncompleteContent(fullContent);

              // 只有在明确检测到截断时才进行续写
              isIncomplete = hasStopReasonLength || (isTokenLimitReached && hasIncompleteContent);

              console.log('续写检测结果:', {
                hasStopReasonLength,
                isTokenLimitReached,
                hasIncompleteContent,
                willContinue: isIncomplete
              });

              if (isIncomplete) {
                console.log('检测到内容不完整，准备续写...');

                // 发送续写标记
                controller.enqueue(encoder.encode('\n\n[继续生成中...]'));

                // 自动续写
                try {
                  // 检测最后的内容片段，用于续写提示
                  const lastLines = fullContent.split('\n').slice(-5).join('\n');
                  const continuePrompt = `请继续完成上述内容，从中断的地方继续。重要要求：
1. 不要重复已有内容
2. 不要添加新的代码块标记
3. 直接从中断处继续输出原始内容
4. 保持与前面内容的连贯性
5. 最后几行内容是：
${lastLines}

请从这里继续，不要重复这些内容。`;

                  const continueMessages = [
                    ...filteredMessages,
                    { role: 'assistant' as const, content: fullContent },
                    { role: 'user' as const, content: continuePrompt }
                  ];

                  await aiClient.streamingChatCompletion(
                    {
                      model,
                      messages: continueMessages,
                    },
                    // 续写内容回调
                    (content: string) => {
                      // 清理续写内容，移除可能的重复部分
                      let cleanContent = content;

                      // 移除续写标记
                      cleanContent = cleanContent.replace(/^\s*\[继续生成中\.\.\.\]\s*/g, '');

                      // 更智能的重复内容检测和移除
                      const lastLines = fullContent.split('\n').slice(-10); // 取最后10行
                      const continueLines = cleanContent.split('\n');

                      // 找到第一个不重复的行
                      let startIndex = 0;
                      for (let i = 0; i < continueLines.length; i++) {
                        const line = continueLines[i].trim();
                        if (line && !lastLines.some(lastLine => lastLine.trim() === line)) {
                          startIndex = i;
                          break;
                        }
                      }

                      // 从第一个不重复的行开始
                      if (startIndex > 0) {
                        cleanContent = continueLines.slice(startIndex).join('\n');
                      }

                      // 移除各种可能的代码块开始标记
                      cleanContent = cleanContent.replace(/^```html\{filename=[^}]+\}\s*/g, '');
                      cleanContent = cleanContent.replace(/^```html\s*/g, '');
                      cleanContent = cleanContent.replace(/^```markdown\{filename=[^}]+\}\s*/g, '');
                      cleanContent = cleanContent.replace(/^```markdown\s*/g, '');
                      cleanContent = cleanContent.replace(/^```md\s*/g, '');
                      cleanContent = cleanContent.replace(/^```\s*/g, '');

                      // 移除结尾的代码块标记
                      cleanContent = cleanContent.replace(/\s*```\s*$/g, '');

                      // 移除中间可能出现的代码块标记
                      cleanContent = cleanContent.replace(/\n```html\{filename=[^}]+\}\s*/g, '\n');
                      cleanContent = cleanContent.replace(/\n```html\s*/g, '\n');
                      cleanContent = cleanContent.replace(/\n```markdown\{filename=[^}]+\}\s*/g, '\n');
                      cleanContent = cleanContent.replace(/\n```markdown\s*/g, '\n');
                      cleanContent = cleanContent.replace(/\n```md\s*/g, '\n');

                      if (cleanContent.trim()) {
                        fullContent += cleanContent; // 重要：将清理后的续写内容添加到完整内容中

                        // 发送续写内容到客户端，标记为续写内容，不添加额外换行
                        controller.enqueue(encoder.encode('[CONTINUE_CONTENT]' + cleanContent));
                      }
                    },
                    // 续写错误回调
                    (error: Error) => {
                      console.error('续写错误:', error);
                      controller.enqueue(encoder.encode('\n\n[续写失败，请手动重试]'));
                      controller.close();
                    },
                    // 续写完成回调
                    (continueResponse) => {
                      console.log('续写完成:', continueResponse);
                      controller.close();
                    }
                  );
                } catch (continueError) {
                  console.error('续写启动错误:', continueError);
                  controller.enqueue(encoder.encode('\n\n[续写失败，请手动重试]'));
                  controller.close();
                }
              } else {
                controller.close();
              }
            },
            // onReasoningContent callback - 流式推理内容
            (reasoningChunk: string) => {
              reasoningContent += reasoningChunk;
              // 实时发送推理内容给前端，使用特殊标记
              controller.enqueue(encoder.encode(`[REASONING_CHUNK]${reasoningChunk}[/REASONING_CHUNK]`));
            }
          );
        } catch (error) {
          console.error('流式聊天启动错误:', error);
          controller.error(error);
        }
      },
      cancel() {
        console.log('流式聊天被取消');
      }
    });

    return new Response(readableStream, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
      },
    });
  } catch (error) {
    console.error('Error in chat-stream API:', error);

    return NextResponse.json({
      error: '流式聊天服务暂时不可用，请稍后再试或检查您的API配置。',
    }, { status: 500 });
  }
}
