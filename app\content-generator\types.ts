// 消息角色
export type MessageRole = 'user' | 'assistant' | 'system' | 'task';

// 消息
export interface Message {
  id: string;
  role: MessageRole;
  content: string;
  timestamp: number;
  // 消息类型，用于UI渲染决策
  type?: 'user' | 'assistant' | 'task' | 'system';
  // 关联的任务ID（如适用）
  taskId?: string;
  // DeepSeek Reasoner 推理内容（可选）
  reasoningContent?: string;
}

// 对话
export interface Conversation {
  id: string;
  messages: Message[];
  timestamp: number;
}

// 文件状态
export type FileStatus = 'waiting' | 'generating' | 'completed' | 'error';

// 视图模式
export type ViewMode = 'code' | 'preview' | 'split';

// 生成的文件
export interface GeneratedFile {
  id: string;
  name: string;
  order?: number; // 添加order属性（可选），用于维护文件顺序
  description?: string;
  content: string;
  contentType: 'html' | 'markdown';
  status: FileStatus;
  viewMode: ViewMode;
  timestamp: number;
  // 版本控制相关字段
  isModified?: boolean;
  versions?: {
    content: string;
    timestamp: number;
    taskNumber?: number; // 关联的任务编号
    taskDescription?: string; // 关联的任务描述
  }[];
  currentVersionIndex?: number;
}

// 模型类型
export type ModelType = 'gpt-3.5-turbo' | 'gpt-4' | 'grok-3-beta' | 'claude-3-opus' | 'claude-3-sonnet' | 'gemini-pro';

// 高级选项
export interface GenerationOptions {
  style: string;
  complexity: string;
  fileCount: number;
  model: ModelType;
}

// 内容生成请求
export interface GenerateContentRequest {
  conversation: Conversation;
  contentType: 'html' | 'markdown';
  options: GenerationOptions;
}

// 内容生成响应
export interface GenerateContentResponse {
  files: GeneratedFile[];
}

// 任务状态
export type TaskStatus = 'pending' | 'in-progress' | 'completed';

// 任务
export interface Task {
  id: string;
  number: number;
  description: string;
  status: TaskStatus;
  result?: string;
  dependencies?: number[]; // 依赖任务的编号
  outputs?: string[];      // 任务输出的文件ID列表
  retryCount?: number;     // 重试计数
  error?: string;          // 错误信息
  subtasks?: Task[];       // 子任务列表
  level?: number;          // 任务层级，0为顶级任务
  parentId?: string;       // 父任务ID
  context?: string;        // 任务的完整上下文信息
}

// 任务执行阶段
export type TaskExecutionPhase = 'planning' | 'executing' | 'summarizing' | 'completed';

// 文件操作类型
export type FileOperationType = 'create' | 'update' | 'delete';
export type FileOperationStatus = 'pending' | 'in-progress' | 'completed';

// 文件操作
export interface FileOperation {
  id: string;
  type: FileOperationType;
  fileName: string;
  fileType: string;
  status: FileOperationStatus;
  taskNumber?: number;
  timestamp: number;
}
