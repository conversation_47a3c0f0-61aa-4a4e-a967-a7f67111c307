'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { extractCodeFromMessage, extractMultipleFilesFromMessage, generateDefaultFileName } from '../lib/code-extractor';
import ConversationPanel from './components/conversation-panel';
import ContentViewerPanel from './components/content-viewer-panel';
import { Conversation, GeneratedFile, FileStatus, ModelType, Task, TaskStatus, TaskExecutionPhase, FileOperation, FileOperationType, FileOperationStatus } from './types';
import AISettings from '@/components/ai-settings';
import { executeTask, generateSummary } from './task-functions';
import { promptTemplates } from './promptTemplates';
import { useAIStore } from '@/lib/ai-store';
import { useCookies } from 'next-client-cookies';
import { createContentStreamProcessor, ContentStreamProcessor } from '@/lib/streaming/content-stream-processor';

/**
 * 流式内容生成器客户端组件
 * 基于原有content-generator-client.tsx，增加流式输出支持
 */
export default function ContentGeneratorStream() {
  console.log('🚀🚀🚀 ContentGeneratorStream 组件已加载 🚀🚀🚀', new Date().toISOString());
  // 复用原有状态管理逻辑
  const [conversation, setConversation] = useState<Conversation>({
    id: `conv-${Date.now()}`,
    messages: [],
    timestamp: Date.now(),
  });

  // 添加对话引用，用于跟踪最新的对话状态
  const conversationRef = useRef<Conversation>({
    id: `conv-${Date.now()}`,
    messages: [],
    timestamp: Date.now(),
  });

  // 当conversation状态更新时，同步更新conversationRef
  useEffect(() => {
    conversationRef.current = conversation;
    console.log('对话状态已更新，当前消息数:', conversation.messages.length);
  }, [conversation]);

  const [generatedFiles, setGeneratedFiles] = useState<GeneratedFile[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [contentType, setContentType] = useState<'html' | 'markdown'>('html');
  const [tasks, setTasks] = useState<Task[]>([]);
  const [executionPhase, setExecutionPhase] = useState<TaskExecutionPhase>('planning');
  const [currentTaskIndex, setCurrentTaskIndex] = useState<number>(-1);
  const [fileOperations, setFileOperations] = useState<FileOperation[]>([]);

  // 流式处理相关状态
  const [streamingContent, setStreamingContent] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [detectedFiles, setDetectedFiles] = useState<{type: 'html' | 'markdown', filename?: string}[]>([]);
  const [streamingProgress, setStreamingProgress] = useState<string>('正在思考...');
  const streamProcessorRef = useRef<ContentStreamProcessor | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 任务执行流式状态
  const [taskStreamingContent, setTaskStreamingContent] = useState<string>('');
  const [isTaskStreaming, setIsTaskStreaming] = useState(false);
  const [currentStreamingTaskId, setCurrentStreamingTaskId] = useState<string | null>(null);

  // 推理内容相关状态
  const [streamingReasoningContent, setStreamingReasoningContent] = useState<string>('');
  const [isStreamingReasoning, setIsStreamingReasoning] = useState<boolean>(false);

  // AI配置
  const { model, customModels, initializeFromCookies } = useAIStore();
  const cookies = useCookies();

  // 初始化AI配置
  useEffect(() => {
    const init = async () => {
      console.log('🔍 ContentGeneratorStream - 初始化AI配置');
      await initializeFromCookies(cookies);
      console.log('🔍 ContentGeneratorStream - AI配置初始化完成，自定义模型数量:', customModels.length);
    };
    init();
  }, [cookies, customModels.length, initializeFromCookies]);

  // 监听自定义模型更新事件
  useEffect(() => {
    const handleCustomModelsUpdated = (event: CustomEvent) => {
      console.log('🔍 ContentGeneratorStream - 收到自定义模型更新事件:', event.detail);
    };

    // 添加事件监听器
    window.addEventListener('custom-models-updated', handleCustomModelsUpdated as EventListener);

    // 清理函数
    return () => {
      window.removeEventListener('custom-models-updated', handleCustomModelsUpdated as EventListener);
    };
  }, []);

  // 布局相关状态
  const [leftPanelWidth, setLeftPanelWidth] = useState(600);
  const [isDragging, setIsDragging] = useState(false);
  const dragStartXRef = useRef(0);
  const leftPanelWidthRef = useRef(leftPanelWidth);

  // 拖拽处理函数
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    dragStartXRef.current = e.clientX;
    leftPanelWidthRef.current = leftPanelWidth;

    // 防止文本选择
    e.preventDefault();
    document.body.style.userSelect = 'none';
  }, [leftPanelWidth]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    const deltaX = e.clientX - dragStartXRef.current;
    const newWidth = Math.max(300, Math.min(1200, leftPanelWidthRef.current + deltaX));
    setLeftPanelWidth(newWidth);
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    document.body.style.userSelect = '';
  }, []);

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 固定布局，防止流式内容影响布局
  const containerStyle = {
    height: '100vh',
    overflow: 'hidden'
  };

  // 样式选项
  const [styleOptions, setStyleOptions] = useState({
    style: '简约现代',
    complexity: '中等',
    fileCount: 1,
  });

  // 上下文窗口配置
  const [contextOptions, setContextOptions] = useState({
    maxMessages: 0,
    keepSystemMessage: true,
  });

  // 生成唯一ID
  const generateUniqueId = useCallback((prefix: string) => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 添加自定义事件监听器，处理文件版本更新
  useEffect(() => {
    const handleVersionUpdate = (event: any) => {
      const { fileId, versions, versionIndex } = event.detail;

      console.log('[ContentGeneratorStream] 收到文件版本更新事件:', {
        fileId,
        versionsCount: versions.length,
        versionIndex,
        versions: versions.map((v: any) => v.taskDescription)
      });

      // 更新generatedFiles状态
      setGeneratedFiles(prev => {
        const updatedFiles = prev.map(file => {
          if (file.id === fileId) {
            return {
              ...file,
              versions,
              currentVersionIndex: versionIndex,
              isModified: true
            };
          }
          return file;
        });

        console.log('[ContentGeneratorStream] 更新文件版本数组:', {
          fileId,
          versionsCount: versions.length,
          totalFiles: updatedFiles.length
        });

        return updatedFiles;
      });
    };

    // 添加事件监听器
    document.addEventListener('file-version-updated', handleVersionUpdate);

    // 清理函数
    return () => {
      document.removeEventListener('file-version-updated', handleVersionUpdate);
    };
  }, []);

  // 文件操作管理
  const addFileOperation = useCallback((operation: Omit<FileOperation, 'id' | 'timestamp'>) => {
    const newOperation: FileOperation = {
      ...operation,
      id: generateUniqueId('op'),
      timestamp: Date.now(),
    };
    setFileOperations(prev => [...prev, newOperation]);
    return newOperation.id;
  }, [generateUniqueId]);

  const updateFileOperationStatus = useCallback((operationId: string, status: FileOperationStatus) => {
    setFileOperations(prev => prev.map(op =>
      op.id === operationId ? { ...op, status } : op
    ));
  }, []);

  // 创建文件版本
  const createFileVersion = useCallback((content: string, description: string, taskNumber?: number) => ({
    content,
    timestamp: Date.now(),
    taskDescription: description,
    taskNumber: taskNumber ?? currentTaskIndex + 1
  }), [currentTaskIndex]);

  // 初始化文件版本历史
  const initializeFileVersions = useCallback((content: string) => ({
    versions: [createFileVersion(content, '初始创建')],
    currentVersionIndex: 0
  }), [createFileVersion]);

  // 流式消息发送处理
  const handleSendMessageStream = async (content: string) => {
    if (!content.trim() || isStreaming) return;

    // 停止当前流（如果有）
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const userMessage = {
      id: `msg-${Date.now()}`,
      role: 'user' as const,
      content: content,
      timestamp: Date.now(),
      type: 'user' as 'user' | 'assistant' | 'task' | 'system'
    };

    const updatedConversation = {
      ...conversation,
      messages: [...conversation.messages, userMessage],
    };

    setConversation(updatedConversation);
    setIsGenerating(true);
    setIsStreaming(true);
    setStreamingContent('');

    try {
      // 根据上下文窗口配置处理消息
      let messagesToSend = [...updatedConversation.messages];

      // 如果设置了最大消息数量限制且大于0
      if (contextOptions.maxMessages > 0 && messagesToSend.length > contextOptions.maxMessages) {
        // 保存系统消息(如果有且需要保留)
        const systemMessages = contextOptions.keepSystemMessage
          ? messagesToSend.filter(msg => msg.role === 'system')
          : [];

        // 获取最近的N条非系统消息
        const recentMessages = messagesToSend
          .filter(msg => msg.role !== 'system')
          .slice(-contextOptions.maxMessages);

        // 合并系统消息和最近消息
        messagesToSend = [...systemMessages, ...recentMessages];

        console.log(`应用上下文窗口: 从 ${updatedConversation.messages.length} 条消息中选择 ${messagesToSend.length} 条`);
      }

      const generatePromptWithFileInstructions = (userInput: string) => {
        const { fileInstructions, multiFileNote } = promptTemplates;
        const instructions = Object.values(fileInstructions).join('\n\n') + '\n' + multiFileNote;
        return `${userInput}\n\n${instructions}`;
      };

      const apiMessages = [
        ...messagesToSend.map(msg => ({ role: msg.role, content: msg.content })),
        { role: 'user' as const, content: generatePromptWithFileInstructions(content) },
      ];

      // 创建新的AbortController
      abortControllerRef.current = new AbortController();

      // 调用流式API
      const response = await fetch('/api/chat-stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: apiMessages, model }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      // 创建流处理器
      streamProcessorRef.current = createContentStreamProcessor({
        onContent: (delta: string, fullContent: string) => {
          // 始终更新流式显示内容，无论是否为续写内容
          // 流处理器已经处理了续写标记的清理
          setStreamingContent(fullContent);

          // 实时检测文件
          const detectedFileTypes = [];

          // 检测HTML文件
          const htmlMatches = fullContent.match(/```html\{filename=([^}]+)\}/g) ||
                            fullContent.match(/```html/g);
          if (htmlMatches) {
            const filenameMatch = fullContent.match(/```html\{filename=([^}]+)\}/);
            detectedFileTypes.push({
              type: 'html' as const,
              filename: filenameMatch ? filenameMatch[1] : 'index.html'
            });
          }

          // 检测Markdown文件
          const markdownMatches = fullContent.match(/```markdown\{filename=([^}]+)\}/g) ||
                                fullContent.match(/```md\{filename=([^}]+)\}/g) ||
                                fullContent.match(/```markdown/g);
          if (markdownMatches) {
            const filenameMatch = fullContent.match(/```(?:markdown|md)\{filename=([^}]+)\}/);
            detectedFileTypes.push({
              type: 'markdown' as const,
              filename: filenameMatch ? filenameMatch[1] : 'index.md'
            });
          }

          // 更新检测到的文件
          setDetectedFiles(detectedFileTypes);

          // 更新进度状态
          if (detectedFileTypes.length > 0) {
            const fileNames = detectedFileTypes.map(f => f.filename).join(', ');
            setStreamingProgress(`正在生成 ${fileNames}...`);
          } else {
            setStreamingProgress('正在思考...');
          }

          // 检测内容类型变化
          const hasHtmlCode = fullContent.includes('```html') ||
                           fullContent.includes('<!DOCTYPE html') ||
                           fullContent.includes('<html');
          const hasMarkdownCode = fullContent.includes('```markdown') ||
                               fullContent.includes('```md') ||
                               fullContent.includes('# ');

          if (hasHtmlCode && contentType !== 'html') {
            setContentType('html');
          } else if (hasMarkdownCode && contentType !== 'markdown') {
            setContentType('markdown');
          }
        },
        onFileDetected: (fileInfo) => {
          console.log('检测到文件:', fileInfo);
        },
        onTaskDetected: (tasks) => {
          console.log('检测到任务:', tasks);
        },
        onError: (error) => {
          console.error('流处理错误:', error);
          setIsStreaming(false);
          setIsGenerating(false);
          setDetectedFiles([]);
          setStreamingProgress('');
        },
        onReasoningContent: (reasoningChunk: string, fullReasoningContent: string) => {
          // 处理推理内容
          setStreamingReasoningContent(fullReasoningContent);
          setIsStreamingReasoning(true);
        },
        onFinish: (finalContent) => {
          console.log('流处理完成');

          // 保存当前的推理内容，因为稍后会被清理
          const currentReasoningContent = streamingReasoningContent;

          // 添加AI回复到对话，包含推理内容
          const messageId = `msg-${Date.now()}`;
          setConversation(prev => ({
            ...prev,
            messages: [
              ...prev.messages,
              {
                id: messageId,
                role: 'assistant' as const,
                content: finalContent,
                timestamp: Date.now(),
                type: 'assistant' as 'user' | 'assistant' | 'task' | 'system',
                reasoningContent: currentReasoningContent || undefined
              }
            ]
          }));

          // 简化任务规划逻辑：参考非流式版本，只在用户发送消息后进行任务提取
          console.log('开始任务提取...');
          const extractedTasks = extractTasksFromResponse(finalContent);
          console.log('提取到的任务数量:', extractedTasks.length);

          if (extractedTasks.length > 0) {
            console.log('设置任务状态...');
            setTasks(extractedTasks);
            setExecutionPhase('executing');
            setCurrentTaskIndex(0);

            console.log('创建Todo.md文件...');
            // 创建Todo.md文件
            updateTodoFile(true);

            console.log('准备执行第一个任务...');
            // 自动开始执行第一个任务
            setTimeout(() => {
              executeNextTask(extractedTasks, 0);
            }, 1000);
          } else {
            console.log('没有提取到任务，处理文件提取...');
            // 处理文件提取
            const extractedFiles = extractMultipleFilesFromMessage(finalContent, messageId);
            if (extractedFiles.length > 0) {
              processExtractedFiles(extractedFiles);
            }
          }

          // 清理流式状态
          setStreamingContent('');
          setIsStreaming(false);
          setIsGenerating(false);
          setDetectedFiles([]);
          setStreamingProgress('');
          setStreamingReasoningContent('');
          setIsStreamingReasoning(false);
        }
      });

      // 开始处理流
      await streamProcessorRef.current.processStream(response);

    } catch (error) {
      console.error('Error in stream processing:', error);

      if (error instanceof Error && error.name !== 'AbortError') {
        setConversation(prev => ({
          ...prev,
          messages: [
            ...prev.messages,
            {
              id: `msg-${Date.now()}`,
              role: 'assistant' as const,
              content: `抱歉，我暂时无法处理您的请求。当前使用的模型是 ${model}。请尝试重新发送消息或更换模型。`,
              timestamp: Date.now(),
              type: 'assistant' as 'user' | 'assistant' | 'task' | 'system'
            }
          ]
        }));
      }

      setStreamingContent('');
      setIsStreaming(false);
      setIsGenerating(false);
      setStreamingReasoningContent('');
      setIsStreamingReasoning(false);
    }
  };

  // 停止流式处理
  const stopStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (streamProcessorRef.current) {
      streamProcessorRef.current.abort();
    }
    setIsStreaming(false);
    setIsGenerating(false);
    setStreamingContent('');
    setStreamingReasoningContent('');
    setIsStreamingReasoning(false);
  }, []);

  // 渲染任务列表
  const renderTaskList = useCallback((tasks: Task[], level = 0): string => {
    return tasks.map(task => {
      const indent = '  '.repeat(level);
      const checked = task.status === 'completed' ? '[x]' : '[ ]';
      const cleanDescription = task.description.replace(/【任务\d+(?:\.\d+)*】/g, '').trim();

      let line = `${indent}- ${checked} ${cleanDescription}`;
      if (task.subtasks && Array.isArray(task.subtasks) && task.subtasks.length > 0) {
        line += '\n' + renderTaskList(task.subtasks, level + 1);
      }
      return line;
    }).join('\n');
  }, []);

  // 检查所有任务是否完成
  const areAllTasksCompleted = useCallback((tasks: Task[]): boolean => {
    return tasks.every(task => {
      if (task.status !== 'completed') return false;
      if (task.subtasks && task.subtasks.length > 0) {
        return areAllTasksCompleted(task.subtasks);
      }
      return true;
    });
  }, []);

  // 标记所有任务为完成
  const markAllCompleted = useCallback((tasks: Task[]): Task[] => {
    return tasks.map(task => ({
      ...task,
      status: 'completed' as TaskStatus,
      subtasks: task.subtasks ? markAllCompleted(task.subtasks) : undefined
    }));
  }, []);

  // 更新Todo文件
  const updateTodoFile = useCallback((createOperation = false) => {
    // 如果任务列表为空，不创建或更新Todo.md文件
    if (tasks.length === 0) {
      return;
    }

    const allCompleted = areAllTasksCompleted(tasks);
    let tasksForMd = [...tasks]; // 创建任务的副本

    if (allCompleted) {
      // 只为显示创建已完成的任务副本，不更新原始状态
      tasksForMd = markAllCompleted(tasks);
    }

    // 去重任务（基于任务编号）
    const deduplicateTasks = (ts: Task[]): Task[] => {
      const taskMap = new Map<number, Task>();

      const processTasks = (taskList: Task[]) => {
        taskList.forEach(task => {
          if (!taskMap.has(task.number) || (taskMap.get(task.number)?.level ?? 0) > (task.level ?? 0)) {
            taskMap.set(task.number, task);
          }
          if (task.subtasks && task.subtasks.length > 0) {
            processTasks(task.subtasks);
          }
        });
      };

      processTasks(ts);

      return Array.from(taskMap.values()).sort((a, b) => a.number - b.number);
    };

    const dedupedTasks = deduplicateTasks(tasksForMd);

    const todoContent = `## 任务清单\n\n${renderTaskList(dedupedTasks)}`;

    const todoFileIndex = generatedFiles.findIndex(file => file.name === 'Todo.md');

    let operationId: string | undefined;

    if (createOperation) {
      setFileOperations(prev => prev.filter(op => op.fileName !== 'Todo.md'));
      const operationType = todoFileIndex >= 0 ? 'update' : 'create';
      operationId = addFileOperation({
        type: operationType as FileOperationType,
        fileName: 'Todo.md',
        fileType: 'markdown',
        status: 'pending',
        taskNumber: undefined
      });
      updateFileOperationStatus(operationId, 'in-progress');
    }

    if (todoFileIndex >= 0) {
      const existingFile = generatedFiles[todoFileIndex];

      const lastVersion = existingFile.versions && existingFile.versions.length > 0
        ? existingFile.versions[existingFile.versions.length - 1]
        : { content: existingFile.content };

      if (lastVersion.content !== todoContent) {
        const newVersion = createFileVersion(todoContent, '更新任务状态');
        const versions = existingFile.versions || [{
          content: existingFile.content,
          timestamp: existingFile.timestamp,
          taskDescription: '初始版本'
        }];

        const updatedTodoFile: GeneratedFile = {
          ...existingFile,
          content: todoContent,
          timestamp: Date.now(),
          versions: [...versions, newVersion],
          currentVersionIndex: versions.length,
          isModified: true
        };

        setGeneratedFiles(prev => {
          const newFiles = [...prev];
          newFiles[todoFileIndex] = updatedTodoFile;
          return newFiles;
        });
      }
    } else {
      const todoFile: GeneratedFile = {
        id: generateUniqueId('file'),
        name: 'Todo.md',
        description: 'Markdown - 任务清单',
        content: todoContent,
        contentType: 'markdown',
        status: 'completed' as FileStatus,
        order: generatedFiles.length,
        viewMode: 'preview',
        timestamp: Date.now(),
        ...initializeFileVersions(todoContent),
        isModified: false
      };

      setGeneratedFiles(prev => [...prev, todoFile]);
    }

    if (allCompleted && operationId) {
      setFileOperations(prev => prev.map(op =>
        op.fileName === 'Todo.md' && op.status !== 'completed'
          ? { ...op, status: 'completed' as FileOperationStatus }
          : op
      ));
    }
  }, [tasks, generatedFiles, setFileOperations, addFileOperation, updateFileOperationStatus, generateUniqueId, renderTaskList, areAllTasksCompleted, markAllCompleted, createFileVersion, initializeFileVersions]);



  // 清理任务描述
  const cleanTaskDescription = (description: string): string => {
    return description
      .replace(/【任务\d+(?:\.\d+)*】/g, '')
      .replace(/^\s*[\d\.\-\*\+]\s*/, '')
      .replace(/^\s*Task\s*\d+\s*[:：]\s*/i, '')
      .replace(/^\s*步骤\s*\d+\s*[:：]\s*/i, '')
      .trim();
  };

  // 验证任务
  const validateTasks = (tasks: Task[]): Task[] => {
    return tasks.filter(task => {
      if (!task.description || task.description.trim().length === 0) {
        console.warn(`任务 ${task.number} 描述为空，已过滤`);
        return false;
      }
      // 移除描述长度限制，保留完整的任务描述
      // 任务描述可能包含详细的输入输出要求，不应该被截断
      return true;
    });
  };

  // 提取任务的完整上下文信息
  const extractTaskContext = (response: string, taskNumber: string): string => {
    // 查找任务标记的位置
    const taskRegex = new RegExp(`【任务${taskNumber}】([^【]*?)(?=【任务\\d+】|$)`, 's');
    const match = taskRegex.exec(response);

    if (match) {
      // 提取任务标记后的所有内容，直到下一个任务标记
      let context = match[1].trim();

      // 清理格式，保留重要信息
      context = context
        .replace(/\n\s*\n/g, '\n') // 移除多余空行
        .replace(/^\s*-\s*/gm, '- ') // 规范化列表格式
        .trim();

      return context;
    }

    return '';
  };

  // 任务提取逻辑（增强版，包含完整上下文）
  const extractTasksFromResponse = (response: string): Task[] => {
    console.log('从模型响应中提取任务...');
    console.log('响应内容前500字符:', response.substring(0, 500));

    const taskMap: Record<string, Task> = {};
    const topLevelTasks: Task[] = [];
    const processedTaskNumbers = new Set<number>(); // 防止重复任务

    // 步骤1：提取所有 <task-list>...</task-list> 标签内内容
    const taskListBlocks: string[] = [];
    const taskListRegex = /<task-list>([\s\S]*?)<\/task-list>/g;
    let taskListMatch;
    while ((taskListMatch = taskListRegex.exec(response)) !== null) {
      taskListBlocks.push(taskListMatch[1]);
    }

    // 步骤2：在每个标签块内提取任务
    const taskProtocolRegex = /【任务(\d+(?:\.\d+)*)】([^\n]+)/g;
    let foundTasks = false;
    for (const block of taskListBlocks) {
      let match;
      while ((match = taskProtocolRegex.exec(block)) !== null) {
        foundTasks = true;
        const taskNumberStr = match[1];
        const taskTitle = match[2].trim();
        const taskParts = taskNumberStr.split('.');
        const taskNumber = parseInt(taskParts[0]);
        const level = taskParts.length - 1;

        // 检查是否已经处理过这个任务编号
        if (processedTaskNumbers.has(taskNumber)) {
          console.log(`任务编号 ${taskNumber} 已存在，跳过重复任务`);
          continue;
        }

        const taskIdentifier = taskNumberStr;
        // 提取完整的任务上下文（仅在当前 block 内）
        const taskContext = extractTaskContext(block, taskNumberStr);
        // 组合任务描述：标题 + 上下文
        let fullTaskDescription = cleanTaskDescription(taskTitle);
        if (taskContext) {
          fullTaskDescription += '\n' + taskContext;
        }
        const task: Task = {
          id: generateUniqueId('task'),
          number: taskNumber,
          description: fullTaskDescription,
          status: 'pending',
          level: level,
          context: taskContext // 保存原始上下文
        };
        taskMap[taskIdentifier] = task;
        processedTaskNumbers.add(taskNumber);
        if (level === 0) {
          topLevelTasks.push(task);
        } else {
          const parentParts = taskParts.slice(0, -1);
          const parentIdentifier = parentParts.join('.');
          if (taskMap[parentIdentifier]) {
            task.parentId = taskMap[parentIdentifier].id;
            if (!taskMap[parentIdentifier].subtasks) {
              taskMap[parentIdentifier] = {
                ...taskMap[parentIdentifier],
                subtasks: []
              };
            }
            taskMap[parentIdentifier].subtasks!.push(task);
          } else {
            console.warn(`找不到任务 ${taskNumberStr} 的父任务 ${parentIdentifier}，将其作为顶级任务处理`);
            topLevelTasks.push(task);
          }
        }
      }
    }

    // 如果没有找到任务，尝试备用方法
    if (topLevelTasks.length === 0) {
      console.log('未找到符合协议的任务标记，尝试使用备用提取方法...');

      // 备用方法1: 数字列表格式
      const fallbackRegex1 = /(?:^|\n)(\s*)(\d+(?:\.\d+)*)[\.、)\:\uff1a]\s*([^\n]+)/g;

      // 备用方法2: Markdown格式的任务列表，支持加粗标记
      // 例如: "1. **封面页**：任务描述"
      const markdownTaskRegex = /(?:^|\n)(\s*)(\d+)\. \*\*([^*]+)\*\*(?:\:|\uff1a)?\s*([^\n]+)?/g;

      const taskNumbers = new Set<number>();
      let taskCounter = 0;

      // 先尝试Markdown格式的任务列表
      console.log('尝试识别Markdown格式的任务列表...');
      let mdMatch;
      while ((mdMatch = markdownTaskRegex.exec(response)) !== null) {
        const indentation = mdMatch[1] || '';
        const level = Math.floor(indentation.length / 2);
        const taskNumber = parseInt(mdMatch[2]);
        const taskTitle = mdMatch[3].trim();
        const taskDetail = mdMatch[4] ? mdMatch[4].trim() : '';

        // 组合任务标题和详情
        let taskDescription = taskTitle;
        if (taskDetail) {
          taskDescription += ': ' + taskDetail;
        }

        if (taskNumbers.has(taskNumber)) {
          console.log(`发现重复任务编号: ${taskNumber}，跳过`);
          continue;
        }

        console.log(`提取到Markdown格式任务: ${taskNumber}. ${taskDescription}`);

        taskNumbers.add(taskNumber);
        taskCounter++;

        const task: Task = {
          id: generateUniqueId('task'),
          number: taskNumber,
          description: taskDescription,
          status: 'pending',
          level
        };

        taskMap[taskNumber.toString()] = task;
        topLevelTasks.push(task);
      }

      // 如果没有找到Markdown格式的任务，尝试常规格式
      if (taskCounter === 0) {
        console.log('未找到Markdown格式任务，尝试常规格式...');

        let fallbackMatch;
        while ((fallbackMatch = fallbackRegex1.exec(response)) !== null) {
          const indentation = fallbackMatch[1] || '';
          const level = Math.floor(indentation.length / 2);
          const taskNumberStr = fallbackMatch[2];
          let taskDescription = fallbackMatch[3].trim();

          const taskParts = taskNumberStr.split('.');
          const taskNumber = parseInt(taskParts[0]);

          if (taskNumbers.has(taskNumber)) {
            console.log(`发现重复任务编号: ${taskNumber}，跳过`);
            continue;
          }

          taskNumbers.add(taskNumber);
          taskCounter++;

          taskDescription = cleanTaskDescription(taskDescription);

          const task: Task = {
            id: generateUniqueId('task'),
            number: taskNumber,
            description: taskDescription,
            status: 'pending',
            level
          };

          taskMap[taskNumber.toString()] = task;
          topLevelTasks.push(task);
        }
      }

      console.log(`使用备用方法提取到 ${taskCounter} 个任务`);
    }

    // 排序任务
    topLevelTasks.sort((a, b) => a.number - b.number);

    // 验证任务
    const validatedTasks = validateTasks(topLevelTasks);

    console.log(`提取到 ${validatedTasks.length} 个顶级任务，总计 ${Object.keys(taskMap).length} 个任务（含子任务）`);

    return validatedTasks;
  };

  // 处理提取的文件 - 支持多文件和版本管理
  const processExtractedFiles = useCallback((extractedFiles: any[], taskDescription?: string) => {
    console.log('处理提取的文件:', extractedFiles.length);

    const updatedFiles: GeneratedFile[] = [];
    const newFiles: GeneratedFile[] = [];

    for (const [index, file] of extractedFiles.entries()) {
      // 改进文件名生成逻辑
      let fileName = file.filename;
      if (!fileName) {
        if (file.contentType === 'html') {
          fileName = extractedFiles.length === 1 ? 'index.html' : `page-${index + 1}.html`;
        } else {
          fileName = extractedFiles.length === 1 ? 'content.md' : `content-${index + 1}.md`;
        }
      }

      console.log(`处理文件 ${index + 1}:`, {
        fileName,
        contentType: file.contentType,
        contentLength: file.content.length,
        taskDescription
      });

      const existingFileIndex = generatedFiles.findIndex(
        f => f.name === fileName && f.contentType === file.contentType
      );

      if (existingFileIndex >= 0) {
        // 更新现有文件
        const existingFile = generatedFiles[existingFileIndex];

        const lastVersion = existingFile.versions && existingFile.versions.length > 0
          ? existingFile.versions[existingFile.versions.length - 1]
          : { content: existingFile.content };

        if (lastVersion.content !== file.content) {
          const versionDescription = taskDescription ||
            (currentTaskIndex >= 0 ? `任务${currentTaskIndex + 1}生成` : '流式生成的内容');

          const newVersion = createFileVersion(file.content, versionDescription, currentTaskIndex + 1);
          const versions = existingFile.versions || [{
            content: existingFile.content,
            timestamp: existingFile.timestamp,
            taskDescription: '初始版本'
          }];

          const updatedFile: GeneratedFile = {
            ...existingFile,
            content: file.content,
            timestamp: Date.now(),
            versions: [...versions, newVersion],
            currentVersionIndex: versions.length, // 这是新版本的索引（因为新版本会被添加到versions数组末尾）
            isModified: true
          };

          // 确保currentVersionIndex指向最新版本
          console.log(`设置文件 ${fileName} 的当前版本索引为 ${versions.length}，总版本数 ${versions.length + 1}`);

          updatedFiles.push(updatedFile);

          console.log(`更新文件 ${fileName}，新增版本 ${versions.length + 1}，当前版本索引: ${updatedFile.currentVersionIndex}`);
        } else {
          console.log(`文件 ${fileName} 内容未变化，跳过更新`);
        }
      } else {
        // 创建新文件
        const versionDescription = taskDescription ||
          (currentTaskIndex >= 0 ? `任务${currentTaskIndex + 1}生成` : '初始创建');

        const newFile: GeneratedFile = {
          id: generateUniqueId('file'),
          name: fileName,
          description: `${file.contentType === 'html' ? 'HTML' : 'Markdown'} - ${fileName}`,
          content: file.content,
          contentType: file.contentType,
          status: 'completed' as FileStatus,
          order: generatedFiles.length + newFiles.length,
          viewMode: 'preview',
          timestamp: Date.now(),
          ...initializeFileVersions(file.content),
          isModified: false
        };

        // 更新初始版本的描述
        if (newFile.versions && newFile.versions.length > 0) {
          newFile.versions[0].taskDescription = versionDescription;
          newFile.versions[0].taskNumber = currentTaskIndex + 1;
        }

        newFiles.push(newFile);

        console.log(`创建新文件 ${fileName}`);
      }
    }

    // 批量更新文件列表
    setGeneratedFiles(prev => {
      // 移除被更新的文件
      const filteredFiles = prev.filter(file =>
        !updatedFiles.some(updatedFile =>
          updatedFile.name === file.name && updatedFile.contentType === file.contentType
        )
      );

      // 合并所有文件
      const allFiles = [...filteredFiles, ...updatedFiles, ...newFiles];

      console.log(`文件列表已更新: 总计${allFiles.length}个文件，新增${newFiles.length}个，更新${updatedFiles.length}个`);

      // 对于更新的文件，确保UI能够正确响应版本变化
      updatedFiles.forEach(updatedFile => {
        console.log(`文件 ${updatedFile.name} 更新完成:`, {
          currentVersionIndex: updatedFile.currentVersionIndex,
          totalVersions: updatedFile.versions?.length || 0,
          isModified: updatedFile.isModified
        });

        // 触发版本更新事件，确保UI组件能够响应
        setTimeout(() => {
          const versionEvent = new CustomEvent('file-version-updated', {
            detail: {
              fileId: updatedFile.id,
              versions: updatedFile.versions || [],
              versionIndex: updatedFile.currentVersionIndex || 0
            },
            bubbles: true,
            cancelable: true
          });
          document.dispatchEvent(versionEvent);
        }, 100); // 延迟100ms确保状态更新完成
      });

      return allFiles;
    });
  }, [generatedFiles, currentTaskIndex, generateUniqueId, createFileVersion, initializeFileVersions]);

  // 版本管理函数
  const handleViewModeChange = (fileId: string, viewMode: 'code' | 'preview' | 'split') => {
    setGeneratedFiles(prev => prev.map(file =>
      file.id === fileId ? { ...file, viewMode } : file
    ));
  };

  /**
   * 处理文件版本切换
   * 1. 找到目标文件
   * 2. 合并同名文件的所有版本历史
   * 3. 按时间排序并切换到指定版本
   * 4. 更新文件内容和版本信息
   */
  const handleVersionChange = useCallback((fileId: string, versionIndex: number) => {
    console.log('[handleVersionChange] 请求切换版本:', { fileId, versionIndex });

    // 1. 找到目标文件，并获取其基本信息
    const targetFile = generatedFiles.find(f => f.id === fileId);
    if (!targetFile) {
      console.error('[handleVersionChange] 未找到目标文件:', fileId);
      return;
    }

    // 2. 合并同名文件的版本历史
    const sameNameFiles = generatedFiles.filter(f =>
      f.name === targetFile.name && f.contentType === targetFile.contentType
    );

    // 3. 合并所有版本并按时间排序
    // 定义版本类型
    type FileVersion = NonNullable<GeneratedFile['versions']>[number];

    // 合并所有版本
    const allVersions = sameNameFiles.reduce<FileVersion[]>((acc, file) => {
      if (file.versions) {
        acc.push(...file.versions);
      } else if (file.content) {
        // 如果文件没有版本历史，将当前内容作为一个版本
        acc.push({
          content: file.content,
          timestamp: file.timestamp,
          taskDescription: '初始版本'
        });
      }
      return acc;
    }, []).sort((a, b) => a.timestamp - b.timestamp);

    // 4. 验证版本索引的有效性
    if (!allVersions.length) {
      console.error('[handleVersionChange] 文件没有任何可用版本:', {
        fileId,
        name: targetFile.name,
        contentType: targetFile.contentType
      });
      return;
    }

    if (versionIndex < 0 || versionIndex >= allVersions.length) {
      console.error('[handleVersionChange] 无效的版本索引:', {
        versionIndex,
        totalVersions: allVersions.length
      });
      return;
    }

    // 5. 获取目标版本
    const targetVersion = allVersions[versionIndex];

    console.log('[handleVersionChange] 切换版本详情:', {
      fileId,
      fileName: targetFile.name,
      fromVersion: targetFile.currentVersionIndex,
      toVersion: versionIndex,
      totalVersions: allVersions.length,
      versionTimestamp: targetVersion.timestamp,
      versionDescription: targetVersion.taskDescription
    });

    // 6. 更新文件内容和版本信息
    setGeneratedFiles(prev => {
      const updatedFiles = prev.map(file => {
        if (file.id === fileId) {
          return {
            ...file,
            content: targetVersion.content,
            versions: allVersions,
            currentVersionIndex: versionIndex,
            isModified: true
          };
        }
        return file;
      });

      console.log('[handleVersionChange] 更新完成:', {
        updatedFileId: fileId,
        newVersionIndex: versionIndex,
        totalFiles: updatedFiles.length
      });

      return updatedFiles;
    });

    // 7. 手动触发版本更新事件，确保UI同步更新
    // 这解决了版本切换后按钮状态不正确的问题
    const versionEvent = new CustomEvent('file-version-updated', {
      detail: {
        fileId,
        versions: allVersions,
        versionIndex
      },
      bubbles: true,
      cancelable: true
    });

    // 使用setTimeout确保React状态更新后再触发事件
    setTimeout(() => {
      document.dispatchEvent(versionEvent);
      console.log('[handleVersionChange] 手动触发版本更新事件:', {
        fileId,
        versionIndex,
        versionsCount: allVersions.length
      });
    }, 0);
  }, [generatedFiles]);

  // 执行下一个任务
  const executeNextTask = useCallback(async (taskList: Task[], taskIndex: number) => {
    if (taskIndex >= taskList.length) {
      // 所有任务执行完成
      setExecutionPhase('completed');
      return;
    }

    const currentTask = taskList[taskIndex];
    if (!currentTask) return;

    // 检查任务是否已经完成或正在执行，避免重复执行
    if (currentTask.status === 'completed' || currentTask.status === 'in-progress') {
      console.log(`任务 ${currentTask.number} 已经${currentTask.status === 'completed' ? '完成' : '正在执行'}，跳过`);
      // 继续执行下一个任务
      executeNextTask(taskList, taskIndex + 1);
      return;
    }

    console.log(`开始执行任务 ${currentTask.number}: ${currentTask.description.split('\n')[0]}`);

    // 更新任务状态
    setTasks(prev => prev.map(t =>
      t.id === currentTask.id ? { ...t, status: 'in-progress' as TaskStatus } : t
    ));

    // 任务执行消息：与非流式版本保持核心内容一致，但增加最小必要的任务类型标识
    const taskExecutionPrompt = `【执行任务${currentTask.number}】
这是一个需要直接执行的具体任务，而非规划任务。

${currentTask.description}`;

    const taskMessage = {
      id: generateUniqueId('msg'),
      role: 'user' as const,
      content: taskExecutionPrompt,
      timestamp: Date.now(),
      type: 'task' as const,
      taskId: currentTask.id
    };

    // 创建用户友好的任务显示消息 - 只显示任务标题，不包含完整描述
    const taskTitle = currentTask.description.split('\n')[0].trim(); // 只取第一行作为标题
    const userFriendlyTaskMessage = {
      id: generateUniqueId('msg'),
      role: 'user' as const,
      content: `执行任务${currentTask.number}：${taskTitle}`,
      timestamp: Date.now(),
      type: 'task' as const,
      taskId: currentTask.id
    };

    try {
      // 使用conversationRef获取最新的对话状态
      // 这解决了React状态更新异步导致的问题
      const currentMessages = [...conversationRef.current.messages];
      console.log(`[任务${currentTask.number}] 使用conversationRef获取当前对话历史消息数: ${currentMessages.length}`);

      // 添加用户友好的任务消息到对话（用于显示）
      setConversation(prev => {
        const updatedMessages = [...prev.messages, userFriendlyTaskMessage];
        console.log(`[任务${currentTask.number}] 添加用户友好任务消息后消息数: ${updatedMessages.length}`);
        return {
          ...prev,
          messages: updatedMessages
        };
      });

      setIsGenerating(true);

      // 上下文管理：与非流式版本保持一致
      console.log(`[任务${currentTask.number}] 准备上下文消息...`);
      console.log(`[任务${currentTask.number}] 对话历史长度: ${currentMessages.length}`);

      // 使用当前对话消息数组加上用户友好的任务消息
      const currentConversation = [...currentMessages, userFriendlyTaskMessage];
      console.log(`[任务${currentTask.number}] 当前对话消息数量(含任务消息): ${currentConversation.length}`);

      // 打印完整的消息历史摘要，帮助调试
      console.log(`[任务${currentTask.number}] 消息历史摘要:`,
        currentMessages.map((msg, idx) => `${idx+1}. ${msg.role}:${msg.content.substring(0, 20)}...`));

      // 根据上下文窗口配置处理消息
      let messagesToSend = [...currentConversation];

      // 如果设置了最大消息数量限制且大于0
      if (contextOptions.maxMessages > 0 && messagesToSend.length > contextOptions.maxMessages) {
        // 保存系统消息(如果有且需要保留)
        const systemMessages = contextOptions.keepSystemMessage
          ? messagesToSend.filter(msg => msg.role === 'system')
          : [];

        // 获取最近的N条非系统消息
        const recentMessages = messagesToSend
          .filter(msg => msg.role !== 'system')
          .slice(-contextOptions.maxMessages);

        // 合并系统消息和最近消息
        messagesToSend = [...systemMessages, ...recentMessages];

        console.log(`[任务${currentTask.number}] 应用上下文窗口: 从 ${currentConversation.length} 条消息中选择 ${messagesToSend.length} 条`);
      }

      console.log(`[任务${currentTask.number}] 构建API消息...`);

      // 替换最后一条消息（用户友好的任务消息）为实际的任务消息
      // 这样保留了完整的对话历史，同时使用完整的任务描述
      const apiMessages = messagesToSend.map((msg, index) => {
        // 如果是最后一条消息（用户友好任务消息），替换为实际任务消息
        if (index === messagesToSend.length - 1 && msg.id === userFriendlyTaskMessage.id) {
          return {
            role: taskMessage.role,
            content: taskMessage.content
          };
        }
        return {
          role: msg.role,
          content: msg.content
        };
      });

      console.log(`[任务${currentTask.number}] 发送给API的消息数量: ${apiMessages.length}`);

      // 输出消息类型统计，帮助调试上下文管理
      const messageTypes = apiMessages.reduce((acc, msg) => {
        acc[msg.role] = (acc[msg.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      console.log(`[任务${currentTask.number}] 消息类型统计:`, messageTypes);

      // 输出最后一条消息的内容前100个字符，确认是否正确替换了任务消息
      if (apiMessages.length > 0) {
        const lastMessage = apiMessages[apiMessages.length - 1];
        console.log(`[任务${currentTask.number}] 最后一条消息(角色:${lastMessage.role})内容前100字符:`,
          lastMessage.content.substring(0, 100) + '...');
      }

      // 调用流式API执行任务
      console.log(`[任务${currentTask.number}] 开始调用API...`);

      // 添加请求调试信息
      const requestBody = {
        messages: apiMessages,
        model,
        debug: {
          taskNumber: currentTask.number,
          messageCount: apiMessages.length,
          originalMessageCount: conversation.messages.length
        }
      };

      const response = await fetch('/api/chat-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      console.log(`[任务${currentTask.number}] API响应状态码: ${response.status}`);


      if (!response.ok) {
        throw new Error('Failed to execute task');
      }

      // 使用流式处理器处理响应（支持续写）
      let fullContent = '';

      // 设置任务流式状态
      setIsTaskStreaming(true);
      setCurrentStreamingTaskId(currentTask.id);
      setTaskStreamingContent('');

      console.log(`[任务${currentTask.number}] 创建流处理器...`);
      const taskStreamProcessor = createContentStreamProcessor({
        onContent: (_delta: string, content: string) => {
          fullContent = content;
          // 实时更新任务流式内容
          setTaskStreamingContent(content);

          // 同时更新右侧预览区域的流式内容
          setStreamingContent(content);
          setIsStreaming(true);

          // 实时检测文件 - 与普通对话一样的检测逻辑
          const currentDetectedFiles = extractMultipleFilesFromMessage(content, 'temp-task-id');
          if (currentDetectedFiles.length > 0) {
            const fileTypes = currentDetectedFiles.map(file => ({
              type: file.contentType as 'html' | 'markdown',
              filename: file.filename
            }));
            setDetectedFiles(fileTypes);

            // 更新流式进度显示
            const fileNames = fileTypes.map(f => f.filename || `${f.type}文件`).join('、');
            setStreamingProgress(`正在生成 ${fileNames}...`);

            // 记录文件检测信息
            if (currentDetectedFiles.length > 0 && currentDetectedFiles.length % 5 === 0) {
              console.log(`[任务${currentTask.number}] 流式过程中检测到文件: ${currentDetectedFiles.length}个`);
              console.log(`[任务${currentTask.number}] 文件类型: ${fileTypes.map(f => `${f.type}(${f.filename})`).join(', ')}`);
            }
          } else {
            setStreamingProgress(`正在执行任务${currentTask.number}...`);
          }

          // 定期记录流式内容长度，不要过于频繁
          if (content.length % 1000 === 0) {
            console.log(`[任务${currentTask.number}] 流式内容当前长度: ${content.length}字符`);
          }
        },
        onReasoningContent: (reasoningChunk: string, fullReasoningContent: string) => {
          // 处理任务执行中的推理内容
          console.log(`[任务${currentTask.number}] 收到推理内容，长度: ${reasoningChunk.length}`);
          setStreamingReasoningContent(fullReasoningContent);
          setIsStreamingReasoning(true);
        },
        onFinish: (finalContent: string) => {
          fullContent = finalContent;
          console.log(`[任务${currentTask.number}] 流式处理完成，最终内容长度: ${finalContent.length}字符`);

          // 记录内容特征
          const hasTaskMarkers = /【任务\d+(?:\.\d+)*】/.test(finalContent);
          const hasCodeBlocks = /```[\s\S]*?```/.test(finalContent);
          const hasHtmlContent = /```html|<!DOCTYPE html|<html/.test(finalContent);
          const hasMarkdownContent = /```markdown|```md|^#\s/.test(finalContent);

          console.log(`[任务${currentTask.number}] 内容特征:`, {
            hasTaskMarkers,
            hasCodeBlocks,
            hasHtmlContent,
            hasMarkdownContent,
            contentPreview: finalContent.substring(0, 100) + '...'
          });

          // 清理任务流式状态
          setIsTaskStreaming(false);
          setCurrentStreamingTaskId(null);
          setTaskStreamingContent('');

          // 清理右侧流式状态
          setStreamingContent('');
          setIsStreaming(false);

          // 清理文件检测状态
          setDetectedFiles([]);
          setStreamingProgress('');

          // 注意：不要在这里清理推理内容状态，因为需要保存到消息中
        },
        onError: (error: Error) => {
          console.error(`[任务${currentTask.number}] 流处理错误:`, error);
          // 清理任务流式状态
          setIsTaskStreaming(false);
          setCurrentStreamingTaskId(null);
          setTaskStreamingContent('');

          // 清理右侧流式状态
          setStreamingContent('');
          setIsStreaming(false);

          // 清理文件检测状态
          setDetectedFiles([]);
          setStreamingProgress('');
        }
      });

      // 处理流式响应
      await taskStreamProcessor.processStream(response);

      // 添加AI回复到对话，包含推理内容
      const messageId = `msg-${Date.now()}`;
      const currentReasoningContent = streamingReasoningContent; // 保存当前推理内容
      const assistantMessage = {
        id: messageId,
        role: 'assistant' as const,
        content: fullContent,
        timestamp: Date.now(),
        type: 'assistant' as 'user' | 'assistant' | 'task' | 'system',
        reasoningContent: currentReasoningContent || undefined
      };

      // 使用conversationRef获取最新的对话状态
      const currentMessageCount = conversationRef.current.messages.length;
      console.log(`[任务${currentTask.number}] 当前对话历史消息数(添加AI回复前): ${currentMessageCount}`);

      // 打印当前消息历史摘要，帮助调试
      console.log(`[任务${currentTask.number}] 消息历史摘要(添加AI回复前):`,
        conversationRef.current.messages.map((msg, idx) => `${idx+1}. ${msg.role}:${msg.content.substring(0, 20)}...`));

      // 提取文件
      const extractedFiles = extractMultipleFilesFromMessage(fullContent, messageId);
      if (extractedFiles.length > 0) {
        const taskDescription = `任务${currentTaskIndex + 1}: ${currentTask.description}`;
        processExtractedFiles(extractedFiles, taskDescription);
      }

      // 更新任务状态为完成
      setTasks(prev => prev.map(t =>
        t.id === currentTask.id ? { ...t, status: 'completed' as TaskStatus, result: fullContent } : t
      ));

      setIsGenerating(false);
      setCurrentTaskIndex(taskIndex + 1);

      // 关键改变：使用Promise来确保对话状态更新完成
      // 先更新对话状态，然后在回调中执行下一个任务
      const updateConversationPromise = new Promise<void>((resolve) => {
        setConversation(prev => {
          const newMessages = [...prev.messages, assistantMessage];
          console.log(`[任务${currentTask.number}] 添加AI回复到对话历史，新消息总数: ${newMessages.length}`);

          // 使用setTimeout确保状态更新完成后才解析Promise
          setTimeout(() => resolve(), 100);

          return {
            ...prev,
            messages: newMessages
          };
        });
      });

      // 等待对话状态更新完成后再执行下一个任务
      updateConversationPromise.then(() => {
        // 使用conversationRef获取最新的对话状态
        const newMessageCount = conversationRef.current.messages.length;
        console.log(`[任务${currentTask.number}] 完成，准备执行下一个任务`);
        console.log(`[任务${currentTask.number}] 对话历史对比: 原始${currentMessageCount} -> 更新后${newMessageCount}`);

        // 打印更新后的消息历史摘要
        console.log(`[任务${currentTask.number}] 消息历史摘要(添加AI回复后):`,
          conversationRef.current.messages.map((msg, idx) => `${idx+1}. ${msg.role}:${msg.content.substring(0, 20)}...`));

        // 清理推理内容状态（在消息保存完成后）
        setStreamingReasoningContent('');
        setIsStreamingReasoning(false);

        // 使用较长的延时确保状态完全更新
        setTimeout(() => {
          console.log(`[任务${currentTask.number}] 开始执行下一个任务，当前对话消息数: ${conversationRef.current.messages.length}`);
          executeNextTask(taskList, taskIndex + 1);
        }, 300);
      });

    } catch (error) {
      console.error('Error executing task:', error);

      // 更新任务状态为失败
      setTasks(prev => prev.map(t =>
        t.id === currentTask.id ? { ...t, status: 'pending' as TaskStatus } : t
      ));

      setIsGenerating(false);
    }
  }, [conversation, model, generateUniqueId, processExtractedFiles, currentTaskIndex]);

  // 同步任务状态到Todo文件
  useEffect(() => {
    // 只有当任务列表不为空时才更新Todo.md
    if (tasks.length > 0) {
      updateTodoFile(false); // 不创建新操作，仅同步内容
    } else {
      // 如果任务列表为空且Todo.md存在，则从生成的文件列表中移除它
      const todoFileIndex = generatedFiles.findIndex(file => file.name === 'Todo.md');
      if (todoFileIndex >= 0) {
        setGeneratedFiles(prev => prev.filter(file => file.name !== 'Todo.md'));
      }
    }
  }, [tasks, updateTodoFile, generatedFiles]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (streamProcessorRef.current) {
        streamProcessorRef.current.abort();
      }
    };
  }, []);

  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white" style={containerStyle}>
      <header className="flex items-center justify-between p-4 border-b border-gray-700 flex-shrink-0">
        <h1 className="text-xl font-bold flex items-center gap-2">
          <svg className="w-6 h-6 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12a3 3 0 1 0 6 0 3 3 0 0 0-6 0z" />
          </svg>
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-400 font-extrabold">
            面向过程文件的AIGC原生内容生成智能体 (流式版)
          </span>
        </h1>
        <div className="flex items-center gap-2">
          {isStreaming && (
            <button
              onClick={stopStreaming}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm"
            >
              停止生成
            </button>
          )}
          <AISettings />
        </div>
      </header>

      <main className="flex flex-1 overflow-hidden min-h-0">
        <div className="flex flex-1 overflow-hidden min-h-0">
          {/* 左侧对话面板 */}
          <div
            className="flex-shrink-0 overflow-hidden bg-gray-800 border-r border-gray-700 min-h-0"
            style={{
              width: `${leftPanelWidth}px`,
              minWidth: `${leftPanelWidth}px`,
              maxWidth: `${leftPanelWidth}px`
            }}
          >
            <ConversationPanel
              conversation={conversation}
              onSendMessage={handleSendMessageStream}
              contentType={contentType}
              setContentType={setContentType}
              styleOptions={styleOptions}
              setStyleOptions={setStyleOptions}
              contextOptions={contextOptions}
              setContextOptions={setContextOptions}
              onGenerateContent={() => {}} // 流式版本不需要单独的生成按钮
              isGenerating={isGenerating}
              tasks={tasks}
              executionPhase={executionPhase}
              fileOperations={fileOperations}
              streamingContent={streamingContent}
              isStreaming={isStreaming}
              detectedFiles={detectedFiles}
              streamingProgress={streamingProgress}
              taskStreamingContent={taskStreamingContent}
              isTaskStreaming={isTaskStreaming}
              currentStreamingTaskId={currentStreamingTaskId}
              streamingReasoningContent={streamingReasoningContent}
              isStreamingReasoning={isStreamingReasoning}
            />
          </div>

          {/* 可拖拽分隔线 */}
          <div
            className="w-4 flex items-center justify-center cursor-col-resize bg-gray-800 hover:bg-indigo-800 flex-shrink-0"
            onMouseDown={handleMouseDown}
          >
            <div className="h-10 w-[2px] bg-cyan-400 rounded-full glow-effect"></div>
          </div>

          {/* 右侧内容预览面板 */}
          <div className="flex-1 overflow-hidden bg-gray-850 min-h-0 min-w-0">
            <ContentViewerPanel
              files={generatedFiles}
              isGenerating={isGenerating}
              onViewModeChange={handleViewModeChange}
              onVersionChange={handleVersionChange}
              streamingContent={streamingContent}
              isStreaming={isStreaming}
            />
          </div>
        </div>
      </main>
    </div>
  );
}
