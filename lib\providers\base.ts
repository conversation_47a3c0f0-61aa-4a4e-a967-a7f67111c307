/**
 * 基础Provider接口
 * 定义所有Provider必须实现的方法和属性
 */

import { ModelProvider } from '../models';

export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatCompletionOptions {
  model: string;
  messages: Message[];
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface ChatCompletionResponse {
  content: string;
  model: string;
  reasoningContent?: string; // DeepSeek Reasoner 特有的推理内容
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface ProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  customModels?: Array<{
    id: string;
    name: string;
    provider: string;
    maxTokens?: number;
    temperature?: number;
  }>;
}

/**
 * 基础Provider抽象类
 * 所有具体Provider实现都应该继承这个类
 */
export abstract class BaseProvider {
  protected config: ProviderConfig;
  protected type: ModelProvider;
  protected customModels: ProviderConfig['customModels'];

  constructor(type: ModelProvider, config: ProviderConfig) {
    this.type = type;
    this.config = config;
    this.customModels = config.customModels || [];
  }

  /**
   * 获取Provider类型
   */
  getType(): ModelProvider {
    return this.type;
  }

  /**
   * 验证配置
   * 确保必要的配置项存在
   */
  abstract validateConfig(): boolean;

  /**
   * 聊天完成方法
   * 发送消息到模型并获取回复
   */
  abstract chatCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse>;

  /**
   * 流式聊天完成方法
   * 发送消息到模型并获取流式回复
   */
  abstract streamingChatCompletion(
    options: ChatCompletionOptions,
    onContent: (content: string) => void,
    onError: (error: Error) => void,
    onFinish: (response: ChatCompletionResponse) => void,
    onReasoningContent?: (reasoningContent: string) => void // 可选回调，用于处理 DeepSeek Reasoner 的推理内容
  ): Promise<void>;
}
