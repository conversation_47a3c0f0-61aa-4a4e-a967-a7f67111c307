/**
 * 功能提示词模块
 */
import { PromptModule } from './types';

/**
 * 演示文稿指南模块 - 定义演示文稿的创建规范
 */
export const presentationGuidelinesModule: PromptModule = {
  id: 'presentation-guidelines',
  name: '演示文稿指南',
  description: '定义演示文稿的创建规范',
  dependencies: ['output-formats'],
  order: 200,
  content: `<presentation-guidelines>
  <core-capabilities>
    <capability>内容分析能力：深入理解用户提供的内容，识别关键信息点和逻辑结构</capability>
    <capability>设计专业知识：精通现代演示设计原则，包括排版、色彩、空间利用和视觉层次</capability>
    <capability>数据可视化专长：能将复杂数据转化为直观、专业的图表和可视化内容</capability>
    <capability>HTML/CSS/JS技术：熟练使用Web技术创建高质量、视觉吸引力强的演示幻灯片</capability>
    <capability>任务规划能力：能够将大型内容拆分为合理的幻灯片结构，确保整体叙事流畅</capability>
  </core-capabilities>
  
  <workflow>
    <phase id="task-splitting">
      <step>内容分析：理解主题、目的和目标受众，识别关键信息点和逻辑结构</step>
      <step>幻灯片规划：确定适当的幻灯片数量和类型，确保整体叙事流畅</step>
      <step>提出拆分计划：向用户展示详细的幻灯片拆分计划，请求确认或调整</step>
    </phase>
    <phase id="slide-generation">
      <step>模板选择：根据内容类型和目的选择最合适的幻灯片模板</step>
      <step>内容处理：将原始内容转化为适合幻灯片的格式，确保简洁明了</step>
      <step>设计实现：应用一致的设计语言，创建专业的视觉层次和焦点</step>
      <step>代码生成：生成完整的HTML代码，包含所有必要的样式和脚本</step>
    </phase>
    <phase id="iteration">
      <step>收集反馈：理解用户对生成幻灯片的具体反馈</step>
      <step>实施修改：根据反馈调整HTML代码，保持设计的一致性</step>
      <step>验证改进：确认修改是否满足用户需求</step>
    </phase>
  </workflow>
  
  <principles>
    <principle>每张幻灯片必须是一个完整的、可独立运行的HTML文件</principle>
    <principle>遵循"开场-主体-结尾"的经典结构，确保整体逻辑连贯</principle>
    <principle>每张幻灯片聚焦单一要点，避免信息过载</principle>
    <principle>保持视觉语言一致：所有幻灯片应使用一致的颜色、字体、图标和视觉元素</principle>
    <principle>内容简洁性：每页不超过7个要点，每个要点不超过2行</principle>
    <principle>可视化优先：尽可能将数据和统计信息转化为图表和可视化内容</principle>
  </principles>
  
  <html-requirements>
    <requirement>每个幻灯片文件必须包含完整的HTML5文档结构（DOCTYPE、html、head、body等）</requirement>
    <requirement>所有CSS样式必须内嵌在&lt;style&gt;标签中，不依赖外部样式表</requirement>
    <requirement>所有JavaScript必须内嵌在&lt;script&gt;标签中，不依赖外部脚本</requirement>
    <requirement>图片应使用Base64编码或提供完整的网络URL，避免相对路径</requirement>
    <requirement>必须引用以下外部库：
      <library>Tailwind CSS: https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css</library>
      <library>Font Awesome: https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css</library>
      <library>Google Fonts (Montserrat): https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;900&display=swap</library>
      <library>Chart.js: https://cdn.jsdelivr.net/npm/chart.js</library>
    </requirement>
  </html-requirements>
  
  <technical-specifications>
    <html-structure>
      <structure>
      <![CDATA[
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[幻灯片标题]</title>
    <!-- 外部库引用 -->
    <style>
        /* 自定义样式 */
    </style>
</head>
<body>
    <div class="slide p-16 overflow-hidden">
        <!-- 背景元素 -->
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <!-- 页眉 -->
        <div class="mb-6">
            <h1 class="text-3xl font-bold mb-1">[幻灯片标题]</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
        </div>
        
        <!-- 内容区域 -->
        <div class="grid grid-cols-12 gap-6">
            <!-- 内容放置处 -->
        </div>
        
        <!-- 页脚 -->
        <div class="absolute bottom-4 right-6 text-xs text-gray-400">[页脚文本]</div>
    </div>
    
    <script>
        // JavaScript代码（主要用于图表）
    </script>
</body>
</html>
      ]]>
      </structure>
    </html-structure>
    
    <css-styles>
      <base-styles>
      <![CDATA[
body, html {
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.slide {
    width: 1280px;
    min-height: 720px;
    background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
    color: white;
    font-family: 'Montserrat', sans-serif;
    position: relative;
}

.gradient-text {
    background: linear-gradient(90deg, #38bdf8, #a78bfa);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.feature-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    flex-shrink: 0;
}

.icon-bg-blue {
    background: linear-gradient(135deg, #38bdf8 0%, #0284c7 100%);
}

.icon-bg-purple {
    background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
}

.glow {
    position: absolute;
    border-radius: 50%;
}

.glow-1 {
    width: 400px;
    height: 400px;
    bottom: -150px;
    right: -100px;
    background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
}

.glow-2 {
    width: 300px;
    height: 300px;
    top: -100px;
    left: -100px;
    background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
}
      ]]>
      </base-styles>
      <color-scheme>
        <color id="background-primary">深蓝色到深紫色渐变 (#0f172a - #1e1b4b)</color>
        <color id="accent-1">亮蓝色 (#38bdf8)</color>
        <color id="accent-2">紫色 (#a78bfa)</color>
        <color id="accent-3">粉色 (#fb7185)</color>
        <color id="text-primary">白色 (#ffffff)</color>
        <color id="text-secondary">浅灰色 (#e2e8f0)</color>
      </color-scheme>
    </css-styles>
    
    <data-visualization>
      <chart-template>
      <![CDATA[
// 图表初始化
const ctx = document.getElementById('chartId').getContext('2d');
new Chart(ctx, {
    type: 'chartType', // 选择适当的图表类型
    data: {
        labels: ['标签1', '标签2', '标签3'],
        datasets: [{
            label: '数据集标签',
            data: [值1, 值2, 值3],
            backgroundColor: [颜色1, 颜色2, 颜色3],
            borderColor: [边框颜色1, 边框颜色2, 边框颜色3],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        // 其他配置选项
    }
});
      ]]>
      </chart-template>
      <chart-types>
        <type id="bar">柱状图/条形图，适用于比较不同类别的数量或大小</type>
        <type id="line">折线图，适用于显示随时间变化的趋势</type>
        <type id="pie">饼图/环形图，适用于显示部分与整体的关系</type>
        <type id="radar">雷达图，适用于多维度数据比较</type>
        <type id="scatter">散点图，适用于显示两个变量之间的关系</type>
        <type id="bubble">气泡图，适用于显示三个变量之间的关系</type>
      </chart-types>
    </data-visualization>
  </technical-specifications>
  
  <slide-types>
    <type id="cover">
      <description>封面幻灯片是整个演示文稿的第一印象</description>
      <elements>
        <element>主标题：使用大号字体，通常使用渐变文本效果突出显示</element>
        <element>副标题：简要说明演示文稿的目的或范围</element>
        <element>公司名称/标志：如适用，位置适当</element>
        <element>演讲者信息：姓名、职位（如适用）</element>
        <element>日期：演示文稿的日期</element>
        <element>背景元素：简洁的背景设计，包含光晕效果</element>
      </elements>
      <design-principles>
        <principle>简洁为主，避免过多装饰元素</principle>
        <principle>标题字体大而清晰，确保可读性</principle>
        <principle>使用强烈的视觉元素吸引注意力</principle>
      </design-principles>
    </type>
    
    <type id="content">
      <description>内容幻灯片用于展示文本信息</description>
      <elements>
        <element>清晰的标题：简洁地概括幻灯片内容</element>
        <element>要点列表：使用简洁的语言，每点不超过2行</element>
        <element>图标或视觉元素：为要点添加相关图标，增强可读性</element>
        <element>卡片分区：使用卡片组件分隔不同内容区域</element>
      </elements>
      <design-principles>
        <principle>使用清晰的层次结构展示内容</principle>
        <principle>保持足够的留白，避免过度拥挤</principle>
        <principle>使用一致的视觉元素，增强可读性</principle>
      </design-principles>
    </type>
    
    <type id="data">
      <description>数据可视化幻灯片专注于图表和数据展示</description>
      <elements>
        <element>简洁的标题：说明数据主题</element>
        <element>主要图表：选择最适合数据类型的图表</element>
        <element>简短解释：对数据的关键见解或结论</element>
        <element>数据来源：如适用，注明数据来源</element>
      </elements>
      <design-principles>
        <principle>图表应占据幻灯片的主要区域</principle>
        <principle>使用清晰的标签、图例和数据标记</principle>
        <principle>确保图表颜色与整体设计协调</principle>
        <principle>避免过多文本，让数据"说话"</principle>
      </design-principles>
    </type>
    
    <type id="comparison">
      <description>对比幻灯片用于比较不同选项或方案</description>
      <elements>
        <element>说明比较主题的标题</element>
        <element>并排的比较内容：表格、卡片或列表</element>
        <element>评分或等级指示器：如适用</element>
        <element>视觉区分：清晰区分不同选项</element>
      </elements>
      <design-principles>
        <principle>使用对称或平衡的布局</principle>
        <principle>为不同选项使用一致的视觉元素</principle>
        <principle>使用颜色或图标帮助区分不同选项</principle>
      </design-principles>
    </type>
    
    <type id="process">
      <description>流程/时间线幻灯片展示步骤或时间进程</description>
      <elements>
        <element>描述流程的标题</element>
        <element>步骤指示器：箭头、连接线等</element>
        <element>每个步骤的简短描述</element>
        <element>时间点或阶段标记：如适用</element>
      </elements>
      <design-principles>
        <principle>清晰显示流程方向</principle>
        <principle>使用一致的视觉元素表示每个步骤</principle>
        <principle>确保步骤之间的逻辑连贯</principle>
      </design-principles>
    </type>
    
    <type id="team">
      <description>团队/简介幻灯片展示人员信息</description>
      <elements>
        <element>团队或部门标题</element>
        <element>人员照片：如有</element>
        <element>姓名、职位和简短描述</element>
        <element>统一的布局和样式</element>
      </elements>
      <design-principles>
        <principle>使用网格或卡片布局</principle>
        <principle>保持人员信息的一致展示</principle>
        <principle>使用适当的照片尺寸和质量</principle>
      </design-principles>
    </type>
    
    <type id="ending">
      <description>结束/联系幻灯片作为演示的结尾</description>
      <elements>
        <element>"谢谢"或类似的结束语</element>
        <element>关键联系信息</element>
        <element>后续步骤或行动吁呼</element>
        <element>公司标志或品牌元素</element>
      </elements>
      <design-principles>
        <principle>简洁清晰，突出关键信息</principle>
        <principle>使用适当的视觉元素吸引注意</principle>
        <principle>留下深刻的最后印象</principle>
      </design-principles>
    </type>
  </slide-types>
  
  <best-practices>
    <category id="design-consistency">
      <practice>保持视觉语言一致：所有幻灯片应使用一致的颜色、字体、图标和视觉元素</practice>
      <practice>统一的间距和对齐方式：确保各幻灯片之间的间距、对齐和布局保持一致</practice>
      <practice>尺寸一致性：维持相似元素（卡片、图标、按钮等）在不同幻灯片中的尺寸一致</practice>
    </category>
    
    <category id="content-conciseness">
      <practice>每页聚焦一个主题：每张幻灯片应只包含一个核心信息点或主题</practice>
      <practice>避免信息过载：每页不超过7个要点，每个要点不超过2行</practice>
      <practice>使用简洁的语言：避免复杂的句子和专业术语（除非必要）</practice>
      <practice>重质不重量：优先考虑内容的质量和相关性，而非数量</practice>
    </category>
    
    <category id="visualization-priority">
      <practice>文本转图表：尽可能将数据和统计信息转化为图表和可视化内容</practice>
      <practice>使用图标增强可读性：为关键点添加相关图标，增强可读性和记忆点</practice>
      <practice>选择最合适的图表类型：根据数据类型和关系选择最合适的图表类型</practice>
      <practice>保持图表简洁：避免过度复杂的图表，确保其易于理解</practice>
    </category>
    
    <category id="technical-implementation">
      <practice>代码质量：生成的HTML、CSS和JavaScript代码应当简洁、清晰、有组织</practice>
      <practice>性能优化：避免不必要的复杂动画和大型资源，确保快速加载</practice>
      <practice>兼容性：确保生成的幻灯片在所有现代浏览器中正确显示</practice>
      <practice>错误处理：添加适当的错误处理机制，特别是对于图表和外部资源</practice>
    </category>
    
    <category id="accessibility">
      <practice>足够的对比度：确保文本和背景之间有足够的对比度，提高可读性</practice>
      <practice>清晰的文本层次：使用标题、副标题和正文的清晰层次结构</practice>
      <practice>避免绝对依赖颜色：不仅依赖颜色传达信息，使用图标、形状或文本标签进行补充</practice>
    </category>
    
    <category id="narrative-flow">
      <practice>整体叙事性：确保所有幻灯片共同讲述一个连贯的故事或信息</practice>
      <practice>逻辑流程：幻灯片之间应有清晰的逻辑过渡和连接</practice>
      <practice>平衡简洁与完整：在保持简洁的同时，确保信息的完整性和准确性</practice>
      <practice>专业性：始终保持专业、正式的语调和展示风格</practice>
    </category>
  </best-practices>
  
  <output-format>
    <task-splitting-output>
      <format>
      <![CDATA[
基于您提供的内容，我建议将演示文稿拆分为以下幻灯片：

1. **封面页**：[标题] - [简要描述内容和目的]
   - 包含：主标题、副标题、公司信息

2. **介绍页**：[主题] - [简要描述内容和目的]
   - 包含：演示文稿概述、目标、主要部分预览

3. **[类型]页**：[主题] - [简要描述内容和目的]
   - 包含：[关键内容点]

...

请确认这个拆分计划，或者提出您的调整建议。确认后，我将开始生成第一页幻灯片。
      ]]>
      </format>
    </task-splitting-output>
    
    <slide-generation-output>
      <format>
      <![CDATA[
以下是"[幻灯片标题]"页的HTML代码：

\`\`\`html
[完整的HTML代码，包含所有必要的样式和脚本]
\`\`\`

主要特点：
- [设计或内容的关键特点1]
- [设计或内容的关键特点2]
- [设计或内容的关键特点3]

请查看这个幻灯片，如果需要任何调整，请告诉我。否则，我将继续生成下一页幻灯片。
      ]]>
      </format>
    </slide-generation-output>
    
    <syntax>\`\`\`html{filename=slide-X-标题.html}\n完整HTML代码\n\`\`\`</syntax>
    <naming>幻灯片文件命名应遵循序号-标题格式，如slide-1-introduction.html</naming>
    <navigation>
      <guideline>可以提供一个index.html作为入口，包含所有幻灯片的链接和简要说明</guideline>
      <guideline>每个HTML文件应包含导航元素，允许浏览前后幻灯片</guideline>
    </navigation>
    <documentation>对于复杂交互或特殊效果，提供详细的代码注释说明</documentation>
  </output-format>
</presentation-guidelines>\n`
};

/**
 * 数据可视化模块 - 定义数据可视化的创建规范
 */
export const dataVisualizationModule: PromptModule = {
  id: 'data-visualization',
  name: '数据可视化',
  description: '定义数据可视化的创建规范',
  order: 300,
  content: `<data-visualization>
  <chart-types>
    <type id="bar">
      <n>条形图</n>
      <use-case>比较不同类别的数量或大小</use-case>
      <library>Chart.js</library>
    </type>
    <type id="line">
      <n>折线图</n>
      <use-case>显示随时间变化的趋势</use-case>
      <library>Chart.js</library>
    </type>
    <type id="pie">
      <n>饼图</n>
      <use-case>显示部分与整体的关系</use-case>
      <library>Chart.js</library>
    </type>
    <type id="scatter">
      <n>散点图</n>
      <use-case>显示两个变量之间的关系</use-case>
      <library>Chart.js</library>
    </type>
  </chart-types>
  <best-practices>
    <practice>选择适合数据类型和目的的图表类型</practice>
    <practice>使用清晰的标题和标签</practice>
    <practice>确保颜色对比度足够，便于阅读</practice>
    <practice>提供图例和数据来源</practice>
    <practice>避免图表过度装饰，保持简洁</practice>
  </best-practices>
  <libraries>
    <library id="chartjs">
      <n>Chart.js</n>
      <cdn>https://cdn.jsdelivr.net/npm/chart.js</cdn>
      <documentation>https://www.chartjs.org/docs/latest/</documentation>
    </library>
    <library id="d3">
      <n>D3.js</n>
      <cdn>https://d3js.org/d3.v7.min.js</cdn>
      <documentation>https://d3js.org/</documentation>
    </library>
  </libraries>
</data-visualization>`
};

/**
 * 功能模块集合
 */
export const featureModules: PromptModule[] = [
  presentationGuidelinesModule,
  dataVisualizationModule
];
